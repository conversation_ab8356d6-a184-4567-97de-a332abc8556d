import { NextRequest, NextResponse } from 'next/server'
import { tokenScraper } from '../../../lib/token-scraper'

export async function GET(request: NextRequest) {
  try {
    console.log('Getting tokens from real-time scraper...')

    // Get tokens from our real-time scraper
    const tokens = tokenScraper.getTrackedTokens()

    // If scraper has no tokens yet, fallback to original API
    if (tokens.length === 0) {
      console.log('Scrap<PERSON> has no tokens yet, using fallback API...')

      const response = await fetch('https://tokentracker-fc80b9e9df85.herokuapp.com/tokens', {
        headers: { 'User-Agent': 'TokenTracker/1.0' }
      })

      if (response.ok) {
        const fallbackTokens = await response.json()
        console.log(`Got ${fallbackTokens.length} tokens from fallback API`)

        return NextResponse.json(fallbackTokens, {
          status: 200,
          headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization',
            'Cache-Control': 'public, s-maxage=30, stale-while-revalidate=60',
            'X-Data-Source': 'fallback'
          },
        })
      }
    }

    console.log(`Returning ${tokens.length} tokens from real-time scraper`)

    return NextResponse.json(tokens, {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Cache-Control': 'public, s-maxage=10, stale-while-revalidate=30',
        'X-Data-Source': 'real-time-scraper'
      },
    })

  } catch (error) {
    console.error('Failed to fetch tokens:', error)

    return NextResponse.json(
      {
        error: 'Failed to fetch tokens',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      {
        status: 500,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        },
      }
    )
  }
}



export async function OPTIONS(request: NextRequest) {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}

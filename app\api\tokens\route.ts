import { NextRequest, NextResponse } from 'next/server'
import database from '../../../lib/database/connection'
import { TrackedToken } from '../../../lib/database/models'

const TOKENTRACKER_API_URL = 'https://tokentracker-fc80b9e9df85.herokuapp.com'

export async function GET(request: NextRequest) {
  try {
    await database.connect()

    // Get all active tracked tokens with their DexScreener data
    const tokens = await database.collections.trackedTokens
      .find({ 'trackingInfo.isActive': true })
      .sort({ 'trackingInfo.firstDetected': -1 })
      .toArray()

    // Transform to the format expected by the frontend
    const transformedTokens = tokens.map(token => transformTokenForAPI(token))

    return NextResponse.json(transformedTokens, {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Cache-Control': 'public, s-maxage=10, stale-while-revalidate=30',
      },
    })
  } catch (error) {
    console.error('Error fetching tokens from database:', error)

    // Fallback to original API if database fails
    try {
      const response = await fetch(`${TOKENTRACKER_API_URL}/tokens`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        cache: 'no-cache',
      })

      if (response.ok) {
        const data = await response.json()

        if (Array.isArray(data)) {
          return NextResponse.json(data, {
            status: 200,
            headers: {
              'Access-Control-Allow-Origin': '*',
              'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
              'Access-Control-Allow-Headers': 'Content-Type, Authorization',
              'Cache-Control': 'public, s-maxage=30, stale-while-revalidate=60',
            },
          })
        }
      }
    } catch (fallbackError) {
      console.error('Fallback API also failed:', fallbackError)
    }

    // Return error response
    return NextResponse.json(
      {
        error: 'Failed to fetch tokens',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      {
        status: 500,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        },
      }
    )
  }
}

function transformTokenForAPI(token: TrackedToken) {
  return {
    token_address: token.tokenAddress,
    name: token.name,
    symbol: token.symbol,
    description: token.description,
    image_url: token.imageUrl,
    website: token.website,
    twitter: token.twitter,
    telegram: token.telegram,
    created_timestamp: token.pumpFunData.createdTimestamp,
    last_trade_timestamp: token.pumpFunData.lastTradeTimestamp,
    usd_market_cap: token.pumpFunData.usdMarketCap,
    market_cap: token.pumpFunData.marketCap,
    raydium_pool: token.pumpFunData.raydiumPool,
    complete: token.pumpFunData.complete,
    virtual_sol_reserves: token.pumpFunData.virtualSolReserves,
    virtual_token_reserves: token.pumpFunData.virtualTokenReserves,
    total_supply: token.pumpFunData.totalSupply,
    is_currently_live: token.pumpFunData.isCurrentlyLive,
    bonding_curve: token.pumpFunData.bondingCurve,
    associated_bonding_curve: token.pumpFunData.associatedBondingCurve,
    creator: token.pumpFunData.creator,
    // Add DexScreener data if available
    dex_screener_data: token.dexScreenerData ? {
      price_usd: token.dexScreenerData.priceUsd,
      price_change_5m: token.dexScreenerData.priceChange.m5,
      price_change_1h: token.dexScreenerData.priceChange.h1,
      price_change_6h: token.dexScreenerData.priceChange.h6,
      price_change_24h: token.dexScreenerData.priceChange.h24,
      volume_5m: token.dexScreenerData.volume.m5,
      volume_1h: token.dexScreenerData.volume.h1,
      volume_6h: token.dexScreenerData.volume.h6,
      volume_24h: token.dexScreenerData.volume.h24,
      liquidity_usd: token.dexScreenerData.liquidity?.usd,
      market_cap_dex: token.dexScreenerData.marketCap,
      last_updated: token.dexScreenerData.lastUpdated
    } : null,
    // Add tracking metadata
    tracking_info: {
      first_detected: token.trackingInfo.firstDetected,
      detection_reason: token.trackingInfo.detectionReason,
      last_updated: token.trackingInfo.lastUpdated,
      update_count: token.trackingInfo.updateCount
    }
  }
}

export async function OPTIONS(request: NextRequest) {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}

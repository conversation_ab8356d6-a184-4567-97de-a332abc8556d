import { NextRequest, NextResponse } from 'next/server'

// Direct API scraping approach - no database needed
const PUMP_FUN_API = 'https://frontend-api.pump.fun'
const DEX_SCREENER_API = 'https://api.dexscreener.com/latest/dex'

export async function GET(request: NextRequest) {
  try {
    console.log('Fetching tokens directly from pump.fun...')

    // Fetch latest tokens from pump.fun
    const pumpFunTokens = await fetchPumpFunTokens()

    // Enrich with DexScreener data
    const enrichedTokens = await enrichWithDexScreenerData(pumpFunTokens)

    return NextResponse.json(enrichedTokens, {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Cache-Control': 'public, s-maxage=30, stale-while-revalidate=60',
      },
    })

  } catch (error) {
    console.error('Error fetching tokens:', error)

    // Fallback to original TokenTracker API
    try {
      const response = await fetch('https://tokentracker-fc80b9e9df85.herokuapp.com/tokens', {
        headers: { 'User-Agent': 'TokenTracker/1.0' }
      })

      if (response.ok) {
        const data = await response.json()
        return NextResponse.json(data, {
          status: 200,
          headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization',
            'Cache-Control': 'public, s-maxage=30, stale-while-revalidate=60',
          },
        })
      }
    } catch (fallbackError) {
      console.error('Fallback also failed:', fallbackError)
    }

    return NextResponse.json(
      { error: 'Failed to fetch tokens' },
      { status: 500 }
    )
  }
}

// Fetch tokens from pump.fun
async function fetchPumpFunTokens() {
  try {
    // Get latest tokens sorted by creation time and recent activity
    const endpoints = [
      `${PUMP_FUN_API}/coins?offset=0&limit=50&sort=created_timestamp&order=DESC`,
      `${PUMP_FUN_API}/coins?offset=0&limit=50&sort=last_trade_timestamp&order=DESC`
    ]

    const allTokens = []
    const seenTokens = new Set()

    for (const endpoint of endpoints) {
      try {
        const response = await fetch(endpoint, {
          headers: { 'User-Agent': 'TokenTracker/1.0' }
        })

        if (response.ok) {
          const tokens = await response.json()

          for (const token of tokens) {
            if (!seenTokens.has(token.mint)) {
              seenTokens.add(token.mint)
              allTokens.push(token)
            }
          }
        }
      } catch (error) {
        console.error(`Error fetching from ${endpoint}:`, error)
      }
    }

    return allTokens
  } catch (error) {
    console.error('Error fetching pump.fun tokens:', error)
    return []
  }
}

// Enrich pump.fun tokens with DexScreener data
async function enrichWithDexScreenerData(pumpFunTokens) {
  const enrichedTokens = []

  for (const token of pumpFunTokens) {
    try {
      // Transform pump.fun token to our format
      const transformedToken = {
        token_address: token.mint,
        name: token.name,
        symbol: token.symbol,
        description: token.description,
        image_url: token.image_uri,
        website: token.website,
        twitter: token.twitter,
        telegram: token.telegram,
        created_timestamp: token.created_timestamp,
        last_trade_timestamp: token.last_trade_timestamp,
        usd_market_cap: token.usd_market_cap,
        market_cap: token.market_cap,
        raydium_pool: token.raydium_pool,
        complete: token.complete,
        virtual_sol_reserves: token.virtual_sol_reserves,
        virtual_token_reserves: token.virtual_token_reserves,
        total_supply: token.total_supply,
        is_currently_live: token.is_currently_live,
        bonding_curve: token.bonding_curve,
        associated_bonding_curve: token.associated_bonding_curve,
        creator: token.creator
      }

      // Try to get DexScreener data
      const dexData = await fetchDexScreenerData(token.mint)
      if (dexData) {
        transformedToken.dex_screener_data = {
          price_usd: dexData.priceUsd,
          price_change_5m: dexData.priceChange?.m5,
          price_change_1h: dexData.priceChange?.h1,
          price_change_6h: dexData.priceChange?.h6,
          price_change_24h: dexData.priceChange?.h24,
          volume_5m: dexData.volume?.m5,
          volume_1h: dexData.volume?.h1,
          volume_6h: dexData.volume?.h6,
          volume_24h: dexData.volume?.h24,
          liquidity_usd: dexData.liquidity?.usd,
          market_cap_dex: dexData.marketCap
        }
      }

      enrichedTokens.push(transformedToken)

      // Rate limiting - small delay between requests
      await new Promise(resolve => setTimeout(resolve, 100))

    } catch (error) {
      console.error(`Error enriching token ${token.mint}:`, error)
      // Add token without DexScreener data
      enrichedTokens.push({
        token_address: token.mint,
        name: token.name,
        symbol: token.symbol,
        description: token.description,
        image_url: token.image_uri,
        website: token.website,
        twitter: token.twitter,
        telegram: token.telegram,
        created_timestamp: token.created_timestamp,
        last_trade_timestamp: token.last_trade_timestamp,
        usd_market_cap: token.usd_market_cap,
        market_cap: token.market_cap,
        raydium_pool: token.raydium_pool,
        complete: token.complete,
        virtual_sol_reserves: token.virtual_sol_reserves,
        virtual_token_reserves: token.virtual_token_reserves,
        total_supply: token.total_supply,
        is_currently_live: token.is_currently_live,
        bonding_curve: token.bonding_curve,
        associated_bonding_curve: token.associated_bonding_curve,
        creator: token.creator
      })
    }
  }

  return enrichedTokens
}

// Fetch DexScreener data for a token
async function fetchDexScreenerData(tokenAddress) {
  try {
    const response = await fetch(`${DEX_SCREENER_API}/tokens/${tokenAddress}`, {
      headers: { 'User-Agent': 'TokenTracker/1.0' }
    })

    if (response.ok) {
      const data = await response.json()
      return data.pairs && data.pairs.length > 0 ? data.pairs[0] : null
    }
  } catch (error) {
    console.error(`Error fetching DexScreener data for ${tokenAddress}:`, error)
  }

  return null
}

export async function OPTIONS(request: NextRequest) {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}

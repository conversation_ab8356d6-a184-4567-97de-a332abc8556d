import { ObjectId } from 'mongodb'

// Core token data structure
export interface TrackedToken {
  _id?: ObjectId
  tokenAddress: string
  name: string
  symbol: string
  description: string
  imageUrl: string
  website?: string
  twitter?: string
  telegram?: string
  
  // Pump.fun specific data
  pumpFunData: {
    mint: string
    bondingCurve: string
    associatedBondingCurve: string
    creator: string
    createdTimestamp: number
    raydiumPool: string | null
    complete: boolean // 100% bonded status
    virtualSolReserves: number
    virtualTokenReserves: number
    totalSupply: number
    marketCap: number
    usdMarketCap: number
    isCurrentlyLive: boolean
    lastTradeTimestamp?: number
  }
  
  // DexScreener data
  dexScreenerData?: {
    pairAddress?: string
    priceUsd?: string
    priceNative?: string
    priceChange: {
      m5?: number
      h1?: number
      h6?: number
      h24?: number
    }
    volume: {
      m5?: number
      h1?: number
      h6?: number
      h24?: number
    }
    liquidity?: {
      usd?: number
      base?: number
      quote?: number
    }
    fdv?: number
    marketCap?: number
    lastUpdated: Date
  }
  
  // Tracking metadata
  trackingInfo: {
    firstDetected: Date
    detectionReason: 'new_launch' | '100_percent_bonded' | 'manual_add'
    lastUpdated: Date
    isActive: boolean
    updateCount: number
  }
  
  // Indexes for efficient querying
  createdAt: Date
  updatedAt: Date
}

// Scraping logs for monitoring
export interface ScrapingLog {
  _id?: ObjectId
  service: 'pump_fun' | 'dex_screener'
  action: 'fetch_new_tokens' | 'update_token_data' | 'check_bonding_status'
  status: 'success' | 'error' | 'partial'
  tokensProcessed: number
  newTokensFound: number
  errors?: string[]
  executionTime: number // milliseconds
  timestamp: Date
}

// System monitoring and health
export interface MonitoringStatus {
  _id?: ObjectId
  service: 'pump_fun_monitor' | 'dex_screener_updater' | 'token_detector'
  status: 'running' | 'stopped' | 'error'
  lastRun: Date
  nextRun?: Date
  runCount: number
  errorCount: number
  lastError?: string
  configuration: {
    intervalMs: number
    batchSize: number
    retryAttempts: number
  }
}

// Price history for charts and analytics
export interface PriceHistory {
  _id?: ObjectId
  tokenAddress: string
  timestamp: Date
  priceUsd: number
  volume24h: number
  marketCap: number
  source: 'dex_screener' | 'pump_fun'
}

// Token detection events
export interface TokenDetectionEvent {
  _id?: ObjectId
  tokenAddress: string
  eventType: 'new_launch' | '100_percent_bonded' | 'raydium_migration'
  detectedAt: Date
  sourceData: any // Raw data from pump.fun or other source
  processed: boolean
  processedAt?: Date
  error?: string
}

// Database collection names
export const COLLECTIONS = {
  TRACKED_TOKENS: 'tracked_tokens',
  SCRAPING_LOGS: 'scraping_logs',
  MONITORING_STATUS: 'monitoring_status',
  PRICE_HISTORY: 'price_history',
  DETECTION_EVENTS: 'detection_events'
} as const

// Database indexes for optimal performance
export const DATABASE_INDEXES = {
  TRACKED_TOKENS: [
    { tokenAddress: 1 }, // Unique index
    { 'trackingInfo.firstDetected': -1 }, // Sort by detection time
    { 'trackingInfo.isActive': 1 }, // Filter active tokens
    { 'pumpFunData.complete': 1 }, // Filter by bonding status
    { 'trackingInfo.detectionReason': 1 }, // Filter by detection reason
    { createdAt: -1 }, // Sort by creation time
    { updatedAt: -1 } // Sort by update time
  ],
  SCRAPING_LOGS: [
    { timestamp: -1 }, // Sort by time
    { service: 1, timestamp: -1 }, // Filter by service and time
    { status: 1 } // Filter by status
  ],
  MONITORING_STATUS: [
    { service: 1 }, // Unique index
    { lastRun: -1 } // Sort by last run
  ],
  PRICE_HISTORY: [
    { tokenAddress: 1, timestamp: -1 }, // Compound index for token price history
    { timestamp: -1 } // Sort by time
  ],
  DETECTION_EVENTS: [
    { tokenAddress: 1 }, // Filter by token
    { detectedAt: -1 }, // Sort by detection time
    { processed: 1 }, // Filter by processing status
    { eventType: 1 } // Filter by event type
  ]
} as const

import database from '../database/connection'
import { TrackedToken, TokenDetectionEvent, MonitoringStatus } from '../database/models'
import { dexScreenerUpdater } from './dexscreener-updater'

// Event emitter for real-time notifications
import { EventEmitter } from 'events'

export interface TokenDetectionResult {
  token: TrackedToken
  event: TokenDetectionEvent
  isNewToken: boolean
}

export class TokenDetector extends EventEmitter {
  private isRunning = false
  private intervalId: NodeJS.Timeout | null = null
  private readonly DETECTION_INTERVAL = 15000 // 15 seconds for real-time detection
  private lastProcessedTimestamp = Date.now()

  constructor() {
    super()
    this.initializeMonitoringStatus()
  }

  /**
   * Start real-time token detection
   */
  async start(): Promise<void> {
    if (this.isRunning) {
      console.log('Token detector is already running')
      return
    }

    console.log('Starting real-time token detector...')
    this.isRunning = true

    await this.updateMonitoringStatus('running')

    // Start detection loop
    this.intervalId = setInterval(async () => {
      await this.detectNewTokens()
    }, this.DETECTION_INTERVAL)

    // Run initial detection
    await this.detectNewTokens()

    console.log(`Token detector started with ${this.DETECTION_INTERVAL}ms interval`)
  }

  /**
   * Stop token detection
   */
  async stop(): Promise<void> {
    if (!this.isRunning) {
      console.log('Token detector is not running')
      return
    }

    console.log('Stopping token detector...')
    this.isRunning = false

    if (this.intervalId) {
      clearInterval(this.intervalId)
      this.intervalId = null
    }

    await this.updateMonitoringStatus('stopped')
    console.log('Token detector stopped')
  }

  /**
   * Main detection function - processes unprocessed detection events
   */
  private async detectNewTokens(): Promise<void> {
    try {
      await database.connect()

      // Get unprocessed detection events
      const unprocessedEvents = await database.collections.detectionEvents
        .find({ 
          processed: false,
          detectedAt: { $gte: new Date(this.lastProcessedTimestamp) }
        })
        .sort({ detectedAt: 1 })
        .toArray()

      if (unprocessedEvents.length === 0) {
        return
      }

      console.log(`Processing ${unprocessedEvents.length} new token detection events`)

      for (const event of unprocessedEvents) {
        try {
          const result = await this.processDetectionEvent(event)
          
          if (result) {
            // Emit real-time event
            this.emit('tokenDetected', result)
            
            // Trigger immediate price data fetch for new tokens
            if (result.isNewToken) {
              setTimeout(async () => {
                await dexScreenerUpdater.updateSpecificTokens([result.token.tokenAddress])
              }, 5000) // Wait 5 seconds for the token to potentially appear on DexScreener
            }
          }

          // Mark event as processed
          await database.collections.detectionEvents.updateOne(
            { _id: event._id },
            { 
              $set: { 
                processed: true, 
                processedAt: new Date() 
              } 
            }
          )

        } catch (error) {
          console.error(`Error processing detection event ${event._id}:`, error)
          
          // Mark event as processed with error
          await database.collections.detectionEvents.updateOne(
            { _id: event._id },
            { 
              $set: { 
                processed: true, 
                processedAt: new Date(),
                error: error.toString()
              } 
            }
          )
        }
      }

      // Update last processed timestamp
      this.lastProcessedTimestamp = Date.now()

    } catch (error) {
      console.error('Error in token detection cycle:', error)
      await this.updateMonitoringStatus('error', error.toString())
    }
  }

  /**
   * Process a single detection event
   */
  private async processDetectionEvent(event: TokenDetectionEvent): Promise<TokenDetectionResult | null> {
    try {
      // Check if token already exists
      const existingToken = await database.collections.trackedTokens.findOne({
        tokenAddress: event.tokenAddress
      })

      if (existingToken) {
        // Token already exists, this might be a status change event
        return {
          token: existingToken,
          event,
          isNewToken: false
        }
      }

      // This is a new token, it should have been created by the pump.fun monitor
      // Let's try to find it again in case there was a race condition
      await this.delay(1000) // Wait 1 second
      
      const newToken = await database.collections.trackedTokens.findOne({
        tokenAddress: event.tokenAddress
      })

      if (newToken) {
        console.log(`New token detected and tracked: ${newToken.symbol} (${newToken.tokenAddress})`)
        
        return {
          token: newToken,
          event,
          isNewToken: true
        }
      }

      console.warn(`Detection event for unknown token: ${event.tokenAddress}`)
      return null

    } catch (error) {
      console.error(`Error processing detection event:`, error)
      throw error
    }
  }

  /**
   * Get recently detected tokens
   */
  async getRecentlyDetectedTokens(hours: number = 24): Promise<TrackedToken[]> {
    try {
      await database.connect()
      
      const startTime = new Date(Date.now() - hours * 60 * 60 * 1000)
      
      return await database.collections.trackedTokens
        .find({
          'trackingInfo.firstDetected': { $gte: startTime }
        })
        .sort({ 'trackingInfo.firstDetected': -1 })
        .toArray()

    } catch (error) {
      console.error('Error getting recently detected tokens:', error)
      return []
    }
  }

  /**
   * Get tokens by detection reason
   */
  async getTokensByDetectionReason(reason: 'new_launch' | '100_percent_bonded'): Promise<TrackedToken[]> {
    try {
      await database.connect()
      
      return await database.collections.trackedTokens
        .find({
          'trackingInfo.detectionReason': reason,
          'trackingInfo.isActive': true
        })
        .sort({ 'trackingInfo.firstDetected': -1 })
        .toArray()

    } catch (error) {
      console.error(`Error getting tokens by detection reason ${reason}:`, error)
      return []
    }
  }

  /**
   * Get detection statistics
   */
  async getDetectionStats(hours: number = 24) {
    try {
      await database.connect()
      
      const startTime = new Date(Date.now() - hours * 60 * 60 * 1000)
      
      const [totalDetected, newLaunches, bondedTokens, detectionEvents] = await Promise.all([
        database.collections.trackedTokens.countDocuments({
          'trackingInfo.firstDetected': { $gte: startTime }
        }),
        
        database.collections.trackedTokens.countDocuments({
          'trackingInfo.firstDetected': { $gte: startTime },
          'trackingInfo.detectionReason': 'new_launch'
        }),
        
        database.collections.trackedTokens.countDocuments({
          'trackingInfo.firstDetected': { $gte: startTime },
          'trackingInfo.detectionReason': '100_percent_bonded'
        }),
        
        database.collections.detectionEvents.countDocuments({
          detectedAt: { $gte: startTime }
        })
      ])

      return {
        totalDetected,
        newLaunches,
        bondedTokens,
        detectionEvents,
        timeframe: `${hours} hours`
      }

    } catch (error) {
      console.error('Error getting detection stats:', error)
      return null
    }
  }

  /**
   * Force detection check for specific token
   */
  async forceDetectionCheck(tokenAddress: string): Promise<TokenDetectionResult | null> {
    try {
      // Create a manual detection event
      const detectionEvent: TokenDetectionEvent = {
        tokenAddress,
        eventType: 'new_launch', // Default type
        detectedAt: new Date(),
        sourceData: { manual: true },
        processed: false
      }

      await database.collections.detectionEvents.insertOne(detectionEvent)
      
      // Process immediately
      return await this.processDetectionEvent(detectionEvent)

    } catch (error) {
      console.error(`Error forcing detection check for ${tokenAddress}:`, error)
      return null
    }
  }

  /**
   * Initialize monitoring status
   */
  private async initializeMonitoringStatus(): Promise<void> {
    try {
      await database.connect()
      
      const status: MonitoringStatus = {
        service: 'token_detector',
        status: 'stopped',
        lastRun: new Date(),
        runCount: 0,
        errorCount: 0,
        configuration: {
          intervalMs: this.DETECTION_INTERVAL,
          batchSize: 100,
          retryAttempts: 3
        }
      }

      await database.collections.monitoringStatus.replaceOne(
        { service: 'token_detector' },
        status,
        { upsert: true }
      )
    } catch (error) {
      console.error('Error initializing monitoring status:', error)
    }
  }

  /**
   * Update monitoring status
   */
  private async updateMonitoringStatus(status: 'running' | 'stopped' | 'error', error?: string): Promise<void> {
    try {
      const update: any = {
        status,
        lastRun: new Date(),
        $inc: { runCount: 1 }
      }

      if (error) {
        update.lastError = error
        update.$inc.errorCount = 1
      }

      await database.collections.monitoringStatus.updateOne(
        { service: 'token_detector' },
        update
      )
    } catch (err) {
      console.error('Error updating monitoring status:', err)
    }
  }

  /**
   * Get monitoring status
   */
  async getStatus(): Promise<MonitoringStatus | null> {
    try {
      await database.connect()
      return await database.collections.monitoringStatus.findOne({ service: 'token_detector' })
    } catch (error) {
      console.error('Error getting monitoring status:', error)
      return null
    }
  }

  /**
   * Utility function for delays
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

// Export singleton instance
export const tokenDetector = new TokenDetector()
export default tokenDetector

'use client'

import { useState, useEffect } from 'react'

interface ScraperStatus {
  isRunning: boolean
  trackedTokensCount: number
  scrapeInterval: number
  timestamp: string
  message: string
}

export default function ScraperMonitor() {
  const [status, setStatus] = useState<ScraperStatus | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchStatus = async () => {
    try {
      const response = await fetch('/api/scraper-status')
      if (response.ok) {
        const data = await response.json()
        setStatus(data)
        setError(null)
      } else {
        setError('Failed to fetch status')
      }
    } catch (err) {
      setError('Network error')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchStatus()
    
    // Refresh every 5 seconds
    const interval = setInterval(fetchStatus, 5000)
    
    return () => clearInterval(interval)
  }, [])

  const controlScraper = async (action: string) => {
    try {
      const response = await fetch('/api/scraper-status', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action })
      })
      
      if (response.ok) {
        // Refresh status after action
        setTimeout(fetchStatus, 1000)
      }
    } catch (err) {
      console.error('Error controlling scraper:', err)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 text-white p-8">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-3xl font-bold mb-8">Token Scraper Monitor</h1>
          <div>Loading...</div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">🔍 Real-Time Token Scraper Monitor</h1>
        
        {error && (
          <div className="bg-red-600 text-white p-4 rounded-lg mb-6">
            Error: {error}
          </div>
        )}

        {status && (
          <div className="space-y-6">
            {/* Status Card */}
            <div className="bg-gray-800 rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-4">Scraper Status</h2>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className={`text-2xl mb-2 ${status.isRunning ? 'text-green-500' : 'text-red-500'}`}>
                    {status.isRunning ? '🟢' : '🔴'}
                  </div>
                  <div className="text-sm text-gray-400">Status</div>
                  <div className="font-semibold">
                    {status.isRunning ? 'Running' : 'Stopped'}
                  </div>
                </div>
                
                <div className="text-center">
                  <div className="text-2xl mb-2 text-blue-500">📊</div>
                  <div className="text-sm text-gray-400">Tracked Tokens</div>
                  <div className="font-semibold text-blue-400">
                    {status.trackedTokensCount}
                  </div>
                </div>
                
                <div className="text-center">
                  <div className="text-2xl mb-2 text-purple-500">⏱️</div>
                  <div className="text-sm text-gray-400">Scrape Interval</div>
                  <div className="font-semibold text-purple-400">
                    {status.scrapeInterval / 1000}s
                  </div>
                </div>
                
                <div className="text-center">
                  <div className="text-2xl mb-2 text-yellow-500">🕐</div>
                  <div className="text-sm text-gray-400">Last Update</div>
                  <div className="font-semibold text-yellow-400">
                    {new Date(status.timestamp).toLocaleTimeString()}
                  </div>
                </div>
              </div>
            </div>

            {/* Control Panel */}
            <div className="bg-gray-800 rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-4">Control Panel</h2>
              <div className="flex space-x-4">
                <button
                  onClick={() => controlScraper('start')}
                  className="bg-green-600 hover:bg-green-700 px-4 py-2 rounded-lg transition-colors"
                >
                  Start Scraper
                </button>
                
                <button
                  onClick={() => controlScraper('stop')}
                  className="bg-red-600 hover:bg-red-700 px-4 py-2 rounded-lg transition-colors"
                >
                  Stop Scraper
                </button>
                
                <button
                  onClick={() => controlScraper('restart')}
                  className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg transition-colors"
                >
                  Restart Scraper
                </button>
                
                <button
                  onClick={fetchStatus}
                  className="bg-gray-600 hover:bg-gray-700 px-4 py-2 rounded-lg transition-colors"
                >
                  Refresh Status
                </button>
              </div>
            </div>

            {/* Info */}
            <div className="bg-gray-800 rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-4">How It Works</h2>
              <div className="space-y-2 text-gray-300">
                <p>🔍 <strong>Constant Monitoring:</strong> Scrapes pump.fun every 30 seconds for new token launches</p>
                <p>🎯 <strong>Smart Detection:</strong> Automatically detects new launches and 100% bonded tokens</p>
                <p>📊 <strong>DexScreener Integration:</strong> Enriches tokens with real-time price and volume data</p>
                <p>⚡ <strong>Real-time Updates:</strong> New tokens appear immediately when detected</p>
                <p>🚀 <strong>Auto-start:</strong> Scraper starts automatically when the server runs</p>
              </div>
            </div>

            {/* Message */}
            {status.message && (
              <div className="bg-blue-600 text-white p-4 rounded-lg">
                {status.message}
              </div>
            )}
          </div>
        )}

        <div className="mt-8 text-center">
          <a 
            href="/all-tokens" 
            className="bg-blue-600 hover:bg-blue-700 px-6 py-3 rounded-lg transition-colors inline-block"
          >
            View Tracked Tokens
          </a>
        </div>
      </div>
    </div>
  )
}

import { NextRequest, NextResponse } from 'next/server'
import { backgroundJobManager } from '../../../../lib/services/background-jobs'
import { tokenDetector } from '../../../../lib/services/token-detector'
import database from '../../../../lib/database/connection'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')

    switch (action) {
      case 'status':
        return await getSystemStatus()
      
      case 'stats':
        return await getSystemStats()
      
      case 'detection-stats':
        const hours = parseInt(searchParams.get('hours') || '24')
        return await getDetectionStats(hours)
      
      case 'recent-tokens':
        const recentHours = parseInt(searchParams.get('hours') || '24')
        return await getRecentTokens(recentHours)
      
      default:
        return await getSystemStatus()
    }

  } catch (error) {
    console.error('Error in monitoring API:', error)
    return NextResponse.json(
      { error: 'Failed to fetch monitoring data' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action, ...params } = body

    switch (action) {
      case 'start-services':
        await backgroundJobManager.initialize()
        return NextResponse.json({ message: 'Services started' })
      
      case 'stop-services':
        await backgroundJobManager.stopAllServices()
        return NextResponse.json({ message: 'Services stopped' })
      
      case 'restart-services':
        await backgroundJobManager.restartAllServices()
        return NextResponse.json({ message: 'Services restarted' })
      
      case 'trigger-detection':
        await tokenDetector.forceDetectionCheck(params.tokenAddress)
        return NextResponse.json({ message: 'Detection triggered' })
      
      case 'cleanup-data':
        await backgroundJobManager.cleanupOldData()
        return NextResponse.json({ message: 'Data cleanup completed' })
      
      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        )
    }

  } catch (error) {
    console.error('Error in monitoring action:', error)
    return NextResponse.json(
      { error: 'Failed to execute action' },
      { status: 500 }
    )
  }
}

async function getSystemStatus() {
  try {
    const status = await backgroundJobManager.getAllServiceStatus()
    
    return NextResponse.json({
      services: status,
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error('Error getting system status:', error)
    throw error
  }
}

async function getSystemStats() {
  try {
    const stats = await backgroundJobManager.getSystemStats()
    
    return NextResponse.json({
      stats,
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error('Error getting system stats:', error)
    throw error
  }
}

async function getDetectionStats(hours: number) {
  try {
    const stats = await tokenDetector.getDetectionStats(hours)
    
    return NextResponse.json({
      detectionStats: stats,
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error('Error getting detection stats:', error)
    throw error
  }
}

async function getRecentTokens(hours: number) {
  try {
    const tokens = await tokenDetector.getRecentlyDetectedTokens(hours)
    
    return NextResponse.json({
      recentTokens: tokens,
      count: tokens.length,
      timeframe: `${hours} hours`,
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error('Error getting recent tokens:', error)
    throw error
  }
}

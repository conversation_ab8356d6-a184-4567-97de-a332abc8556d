const { MongoClient } = require('mongodb')

const MONGODB_URI = 'mongodb+srv://wasay:<EMAIL>/Rader'
const TOKENTRACKER_API = 'https://tokentracker-fc80b9e9df85.herokuapp.com/tokens'

async function initializeSystem() {
  console.log('🚀 Initializing Token Tracker System...')
  
  let client
  
  try {
    // Connect to MongoDB
    console.log('📊 Connecting to MongoDB...')
    client = new MongoClient(MONGODB_URI)
    await client.connect()
    const db = client.db('Rader')
    console.log('✅ Connected to MongoDB')

    // Check if we already have tokens
    const existingTokens = await db.collection('tracked_tokens').countDocuments()
    console.log(`Found ${existingTokens} existing tokens in database`)

    if (existingTokens === 0) {
      console.log('📥 Fetching tokens from TokenTracker API...')
      
      // Fetch tokens from original API
      const response = await fetch(TOKENTRACKER_API)
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      const tokens = await response.json()
      console.log(`Found ${tokens.length} tokens to migrate`)

      // Migrate tokens to database
      let migratedCount = 0
      
      for (const token of tokens) {
        try {
          const now = new Date()
          
          const trackedToken = {
            tokenAddress: token.token_address,
            name: token.name,
            symbol: token.symbol,
            description: token.description,
            imageUrl: token.image_url,
            website: token.website || undefined,
            twitter: token.twitter || undefined,
            telegram: token.telegram || undefined,
            
            pumpFunData: {
              mint: token.token_address,
              bondingCurve: token.bonding_curve || '',
              associatedBondingCurve: token.associated_bonding_curve || '',
              creator: token.creator || '',
              createdTimestamp: token.created_timestamp,
              raydiumPool: token.raydium_pool,
              complete: token.complete || false,
              virtualSolReserves: parseFloat(token.virtual_sol_reserves) || 0,
              virtualTokenReserves: parseFloat(token.virtual_token_reserves) || 0,
              totalSupply: token.total_supply,
              marketCap: token.usd_market_cap || 0,
              usdMarketCap: token.usd_market_cap,
              isCurrentlyLive: true,
              lastTradeTimestamp: token.last_trade_timestamp
            },
            
            trackingInfo: {
              firstDetected: now,
              detectionReason: 'manual_add',
              lastUpdated: now,
              isActive: true,
              updateCount: 1
            },
            
            createdAt: now,
            updatedAt: now
          }

          await db.collection('tracked_tokens').insertOne(trackedToken)
          migratedCount++
          
          if (migratedCount % 10 === 0) {
            console.log(`Migrated ${migratedCount} tokens...`)
          }

        } catch (error) {
          console.error(`Error migrating token ${token.token_address}:`, error.message)
        }
      }

      console.log(`✅ Migration completed: ${migratedCount} tokens migrated`)
    }

    // Create indexes for performance
    console.log('📊 Creating database indexes...')
    
    const trackedTokensCollection = db.collection('tracked_tokens')
    
    try {
      await trackedTokensCollection.createIndex({ tokenAddress: 1 }, { unique: true })
      await trackedTokensCollection.createIndex({ 'trackingInfo.firstDetected': -1 })
      await trackedTokensCollection.createIndex({ 'trackingInfo.isActive': 1 })
      await trackedTokensCollection.createIndex({ 'pumpFunData.complete': 1 })
      console.log('✅ Database indexes created')
    } catch (error) {
      console.log('ℹ️ Indexes already exist or creation failed:', error.message)
    }

    // Initialize monitoring status
    console.log('⚙️ Initializing monitoring status...')
    
    const monitoringCollection = db.collection('monitoring_status')
    
    const services = [
      'pump_fun_monitor',
      'dex_screener_updater', 
      'token_detector'
    ]

    for (const service of services) {
      await monitoringCollection.replaceOne(
        { service },
        {
          service,
          status: 'stopped',
          lastRun: new Date(),
          runCount: 0,
          errorCount: 0,
          configuration: {
            intervalMs: service === 'pump_fun_monitor' ? 30000 : service === 'dex_screener_updater' ? 60000 : 15000,
            batchSize: 50,
            retryAttempts: 3
          }
        },
        { upsert: true }
      )
    }

    console.log('✅ Monitoring status initialized')

    // Final stats
    const finalTokenCount = await db.collection('tracked_tokens').countDocuments()
    console.log('')
    console.log('🎉 System initialization completed!')
    console.log(`📊 Total tokens in database: ${finalTokenCount}`)
    console.log('')
    console.log('Next steps:')
    console.log('1. Visit http://localhost:3001/all-tokens to see the tokens')
    console.log('2. Visit http://localhost:3001/admin to start background services')
    console.log('3. The system will automatically start monitoring pump.fun and updating prices')
    console.log('')

  } catch (error) {
    console.error('❌ Initialization failed:', error)
    process.exit(1)
  } finally {
    if (client) {
      await client.close()
    }
  }
}

// Run the initialization
initializeSystem().catch(console.error)

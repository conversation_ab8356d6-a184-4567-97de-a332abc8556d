// Auto-start the token scraper when the server starts
import { tokenScraper } from './token-scraper'

let isInitialized = false

export function initializeTokenScraper() {
  if (isInitialized) {
    console.log('⚠️ Token scraper already initialized')
    return
  }

  console.log('🚀 Auto-starting token scraper...')
  
  // Start the scraper
  tokenScraper.start()
  
  isInitialized = true
  
  console.log('✅ Token scraper auto-started successfully')
}

// Auto-initialize when this module is imported (server-side only)
if (typeof window === 'undefined') {
  console.log('🔧 Auto-start module loaded, scheduling scraper initialization...')
  // Add a small delay to ensure the server is ready
  setTimeout(() => {
    console.log('⏰ Auto-start timeout reached, initializing scraper...')
    initializeTokenScraper()
  }, 3000) // 3 second delay
}

// Handle graceful shutdown
if (typeof process !== 'undefined') {
  process.on('SIGTERM', () => {
    console.log('Received SIGTERM, stopping token scraper...')
    tokenScraper.stop()
    process.exit(0)
  })

  process.on('SIGINT', () => {
    console.log('Received SIGINT, stopping token scraper...')
    tokenScraper.stop()
    process.exit(0)
  })
}

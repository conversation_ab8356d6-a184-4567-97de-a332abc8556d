import database from '../database/connection'
import { TrackedToken, TokenDetectionEvent, ScrapingLog, MonitoringStatus } from '../database/models'

// Pump.fun API response types
interface PumpFunTokenResponse {
  mint: string
  name: string
  symbol: string
  description: string
  image_uri: string
  metadata_uri: string
  twitter: string | null
  telegram: string | null
  bonding_curve: string
  associated_bonding_curve: string
  creator: string
  created_timestamp: number
  raydium_pool: string | null
  complete: boolean
  virtual_sol_reserves: number
  virtual_token_reserves: number
  total_supply: number
  website: string | null
  show_name: boolean
  king_of_the_hill_timestamp: number | null
  market_cap: number
  reply_count: number
  last_reply: number
  nsfw: boolean
  market_id: string | null
  inverted: boolean | null
  is_currently_live: boolean
  username: string | null
  profile_image: string | null
  usd_market_cap: number
  last_trade_timestamp?: number
}

export class PumpFunMonitor {
  private isRunning = false
  private intervalId: NodeJS.Timeout | null = null
  private readonly PUMP_FUN_API = 'https://frontend-api.pump.fun'
  private readonly MONITOR_INTERVAL = 30000 // 30 seconds
  private readonly BATCH_SIZE = 100

  constructor() {
    this.initializeMonitoringStatus()
  }

  /**
   * Start the monitoring service
   */
  async start(): Promise<void> {
    if (this.isRunning) {
      console.log('Pump.fun monitor is already running')
      return
    }

    console.log('Starting Pump.fun monitor...')
    this.isRunning = true

    // Update monitoring status
    await this.updateMonitoringStatus('running')

    // Start the monitoring loop
    this.intervalId = setInterval(async () => {
      await this.monitorTokens()
    }, this.MONITOR_INTERVAL)

    // Run initial scan
    await this.monitorTokens()

    console.log(`Pump.fun monitor started with ${this.MONITOR_INTERVAL}ms interval`)
  }

  /**
   * Stop the monitoring service
   */
  async stop(): Promise<void> {
    if (!this.isRunning) {
      console.log('Pump.fun monitor is not running')
      return
    }

    console.log('Stopping Pump.fun monitor...')
    this.isRunning = false

    if (this.intervalId) {
      clearInterval(this.intervalId)
      this.intervalId = null
    }

    await this.updateMonitoringStatus('stopped')
    console.log('Pump.fun monitor stopped')
  }

  /**
   * Main monitoring function
   */
  private async monitorTokens(): Promise<void> {
    const startTime = Date.now()
    let tokensProcessed = 0
    let newTokensFound = 0
    const errors: string[] = []

    try {
      console.log('Starting Pump.fun monitoring cycle...')

      // Fetch latest tokens from pump.fun
      const latestTokens = await this.fetchLatestTokens()
      tokensProcessed = latestTokens.length

      // Process each token
      for (const token of latestTokens) {
        try {
          const isNew = await this.processToken(token)
          if (isNew) {
            newTokensFound++
          }
        } catch (error) {
          const errorMsg = `Error processing token ${token.mint}: ${error}`
          errors.push(errorMsg)
          console.error(errorMsg)
        }
      }

      // Check for bonding status changes in existing tokens
      await this.checkBondingStatusChanges()

      // Log successful monitoring cycle
      await this.logScrapingResult('success', tokensProcessed, newTokensFound, errors, Date.now() - startTime)

      console.log(`Monitoring cycle completed: ${tokensProcessed} tokens processed, ${newTokensFound} new tokens found`)

    } catch (error) {
      const errorMsg = `Pump.fun monitoring error: ${error}`
      errors.push(errorMsg)
      console.error(errorMsg)

      await this.logScrapingResult('error', tokensProcessed, newTokensFound, errors, Date.now() - startTime)
      await this.updateMonitoringStatus('error', errorMsg)
    }
  }

  /**
   * Fetch latest tokens from pump.fun
   */
  private async fetchLatestTokens(): Promise<PumpFunTokenResponse[]> {
    try {
      // Fetch from multiple endpoints to get comprehensive data
      const endpoints = [
        `${this.PUMP_FUN_API}/coins?offset=0&limit=${this.BATCH_SIZE}&sort=created_timestamp&order=DESC`,
        `${this.PUMP_FUN_API}/coins?offset=0&limit=${this.BATCH_SIZE}&sort=last_trade_timestamp&order=DESC`
      ]

      const allTokens: PumpFunTokenResponse[] = []
      const seenTokens = new Set<string>()

      for (const endpoint of endpoints) {
        try {
          const response = await fetch(endpoint, {
            headers: {
              'User-Agent': 'TokenTracker/1.0',
              'Accept': 'application/json'
            }
          })

          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`)
          }

          const tokens: PumpFunTokenResponse[] = await response.json()
          
          // Deduplicate tokens
          for (const token of tokens) {
            if (!seenTokens.has(token.mint)) {
              seenTokens.add(token.mint)
              allTokens.push(token)
            }
          }
        } catch (error) {
          console.error(`Error fetching from ${endpoint}:`, error)
        }
      }

      return allTokens
    } catch (error) {
      console.error('Error fetching latest tokens:', error)
      throw error
    }
  }

  /**
   * Process a single token and determine if it should be tracked
   */
  private async processToken(token: PumpFunTokenResponse): Promise<boolean> {
    try {
      await database.connect()
      const collection = database.collections.trackedTokens

      // Check if token already exists
      const existingToken = await collection.findOne({ tokenAddress: token.mint })

      if (existingToken) {
        // Update existing token data
        await this.updateExistingToken(existingToken, token)
        return false
      }

      // Determine if this is a new token worth tracking
      const shouldTrack = this.shouldTrackToken(token)
      
      if (shouldTrack.track) {
        await this.addNewToken(token, shouldTrack.reason)
        return true
      }

      return false
    } catch (error) {
      console.error(`Error processing token ${token.mint}:`, error)
      throw error
    }
  }

  /**
   * Determine if a token should be tracked
   */
  private shouldTrackToken(token: PumpFunTokenResponse): { track: boolean; reason: 'new_launch' | '100_percent_bonded' | null } {
    const now = Date.now()
    const tokenAge = now - token.created_timestamp
    const isRecentLaunch = tokenAge < 3600000 // Less than 1 hour old
    const is100PercentBonded = token.complete && token.raydium_pool !== null

    // Track if it's a recent launch
    if (isRecentLaunch) {
      return { track: true, reason: 'new_launch' }
    }

    // Track if it just hit 100% bonded
    if (is100PercentBonded) {
      return { track: true, reason: '100_percent_bonded' }
    }

    return { track: false, reason: null }
  }

  /**
   * Add a new token to tracking
   */
  private async addNewToken(token: PumpFunTokenResponse, reason: 'new_launch' | '100_percent_bonded'): Promise<void> {
    try {
      const now = new Date()
      
      const trackedToken: TrackedToken = {
        tokenAddress: token.mint,
        name: token.name,
        symbol: token.symbol,
        description: token.description,
        imageUrl: token.image_uri,
        website: token.website || undefined,
        twitter: token.twitter || undefined,
        telegram: token.telegram || undefined,
        
        pumpFunData: {
          mint: token.mint,
          bondingCurve: token.bonding_curve,
          associatedBondingCurve: token.associated_bonding_curve,
          creator: token.creator,
          createdTimestamp: token.created_timestamp,
          raydiumPool: token.raydium_pool,
          complete: token.complete,
          virtualSolReserves: token.virtual_sol_reserves,
          virtualTokenReserves: token.virtual_token_reserves,
          totalSupply: token.total_supply,
          marketCap: token.market_cap,
          usdMarketCap: token.usd_market_cap,
          isCurrentlyLive: token.is_currently_live,
          lastTradeTimestamp: token.last_trade_timestamp
        },
        
        trackingInfo: {
          firstDetected: now,
          detectionReason: reason,
          lastUpdated: now,
          isActive: true,
          updateCount: 1
        },
        
        createdAt: now,
        updatedAt: now
      }

      // Insert token
      await database.collections.trackedTokens.insertOne(trackedToken)

      // Log detection event
      const detectionEvent: TokenDetectionEvent = {
        tokenAddress: token.mint,
        eventType: reason,
        detectedAt: now,
        sourceData: token,
        processed: true,
        processedAt: now
      }
      
      await database.collections.detectionEvents.insertOne(detectionEvent)

      console.log(`New token tracked: ${token.symbol} (${token.mint}) - Reason: ${reason}`)
      
    } catch (error) {
      console.error(`Error adding new token ${token.mint}:`, error)
      throw error
    }
  }

  /**
   * Update existing token with latest data
   */
  private async updateExistingToken(existingToken: TrackedToken, newData: PumpFunTokenResponse): Promise<void> {
    try {
      const now = new Date()
      
      // Check for significant changes
      const bondingStatusChanged = existingToken.pumpFunData.complete !== newData.complete
      const raydiumPoolAdded = !existingToken.pumpFunData.raydiumPool && newData.raydium_pool

      // Update the token data
      await database.collections.trackedTokens.updateOne(
        { tokenAddress: existingToken.tokenAddress },
        {
          $set: {
            'pumpFunData.raydiumPool': newData.raydium_pool,
            'pumpFunData.complete': newData.complete,
            'pumpFunData.virtualSolReserves': newData.virtual_sol_reserves,
            'pumpFunData.virtualTokenReserves': newData.virtual_token_reserves,
            'pumpFunData.marketCap': newData.market_cap,
            'pumpFunData.usdMarketCap': newData.usd_market_cap,
            'pumpFunData.isCurrentlyLive': newData.is_currently_live,
            'pumpFunData.lastTradeTimestamp': newData.last_trade_timestamp,
            'trackingInfo.lastUpdated': now,
            updatedAt: now
          },
          $inc: {
            'trackingInfo.updateCount': 1
          }
        }
      )

      // Log significant events
      if (bondingStatusChanged && newData.complete) {
        const detectionEvent: TokenDetectionEvent = {
          tokenAddress: newData.mint,
          eventType: '100_percent_bonded',
          detectedAt: now,
          sourceData: newData,
          processed: true,
          processedAt: now
        }
        
        await database.collections.detectionEvents.insertOne(detectionEvent)
        console.log(`Token reached 100% bonded: ${newData.symbol} (${newData.mint})`)
      }

      if (raydiumPoolAdded) {
        const detectionEvent: TokenDetectionEvent = {
          tokenAddress: newData.mint,
          eventType: 'raydium_migration',
          detectedAt: now,
          sourceData: newData,
          processed: true,
          processedAt: now
        }
        
        await database.collections.detectionEvents.insertOne(detectionEvent)
        console.log(`Token migrated to Raydium: ${newData.symbol} (${newData.mint})`)
      }
      
    } catch (error) {
      console.error(`Error updating token ${existingToken.tokenAddress}:`, error)
      throw error
    }
  }

  /**
   * Check bonding status changes for existing tokens
   */
  private async checkBondingStatusChanges(): Promise<void> {
    try {
      // Get tokens that are not yet 100% bonded
      const incompleteTokens = await database.collections.trackedTokens
        .find({ 'pumpFunData.complete': false, 'trackingInfo.isActive': true })
        .toArray()

      for (const token of incompleteTokens) {
        try {
          // Fetch latest data for this token
          const response = await fetch(`${this.PUMP_FUN_API}/coins/${token.tokenAddress}`)
          
          if (response.ok) {
            const latestData: PumpFunTokenResponse = await response.json()
            await this.updateExistingToken(token, latestData)
          }
        } catch (error) {
          console.error(`Error checking bonding status for ${token.tokenAddress}:`, error)
        }
      }
    } catch (error) {
      console.error('Error checking bonding status changes:', error)
    }
  }

  /**
   * Initialize monitoring status in database
   */
  private async initializeMonitoringStatus(): Promise<void> {
    try {
      await database.connect()
      
      const status: MonitoringStatus = {
        service: 'pump_fun_monitor',
        status: 'stopped',
        lastRun: new Date(),
        runCount: 0,
        errorCount: 0,
        configuration: {
          intervalMs: this.MONITOR_INTERVAL,
          batchSize: this.BATCH_SIZE,
          retryAttempts: 3
        }
      }

      await database.collections.monitoringStatus.replaceOne(
        { service: 'pump_fun_monitor' },
        status,
        { upsert: true }
      )
    } catch (error) {
      console.error('Error initializing monitoring status:', error)
    }
  }

  /**
   * Update monitoring status
   */
  private async updateMonitoringStatus(status: 'running' | 'stopped' | 'error', error?: string): Promise<void> {
    try {
      const update: any = {
        status,
        lastRun: new Date(),
        $inc: { runCount: 1 }
      }

      if (error) {
        update.lastError = error
        update.$inc.errorCount = 1
      }

      await database.collections.monitoringStatus.updateOne(
        { service: 'pump_fun_monitor' },
        update
      )
    } catch (err) {
      console.error('Error updating monitoring status:', err)
    }
  }

  /**
   * Log scraping results
   */
  private async logScrapingResult(
    status: 'success' | 'error' | 'partial',
    tokensProcessed: number,
    newTokensFound: number,
    errors: string[],
    executionTime: number
  ): Promise<void> {
    try {
      const log: ScrapingLog = {
        service: 'pump_fun',
        action: 'fetch_new_tokens',
        status,
        tokensProcessed,
        newTokensFound,
        errors: errors.length > 0 ? errors : undefined,
        executionTime,
        timestamp: new Date()
      }

      await database.collections.scrapingLogs.insertOne(log)
    } catch (error) {
      console.error('Error logging scraping result:', error)
    }
  }

  /**
   * Get monitoring status
   */
  async getStatus(): Promise<MonitoringStatus | null> {
    try {
      await database.connect()
      return await database.collections.monitoringStatus.findOne({ service: 'pump_fun_monitor' })
    } catch (error) {
      console.error('Error getting monitoring status:', error)
      return null
    }
  }
}

// Export singleton instance
export const pumpFunMonitor = new PumpFunMonitor()
export default pumpFunMonitor

'use client'

import { useState, useEffect } from 'react'
import { useRealTimeUpdates, useTokenDetectionNotifications } from '../../lib/hooks/useRealTimeUpdates'

interface ServiceStatus {
  service: string
  status: 'running' | 'stopped' | 'error'
  lastRun: string
  runCount: number
  errorCount: number
  lastError?: string
}

interface SystemStats {
  database: {
    collections: {
      trackedTokens: number
      scrapingLogs: number
      monitoringStatus: number
      priceHistory: number
      detectionEvents: number
    }
  }
  recentActivity: {
    scrapingLogs: any[]
    detectionEvents: any[]
  }
}

interface DetectionStats {
  totalDetected: number
  newLaunches: number
  bondedTokens: number
  detectionEvents: number
  timeframe: string
}

export default function AdminDashboard() {
  const [services, setServices] = useState<Record<string, ServiceStatus>>({})
  const [systemStats, setSystemStats] = useState<SystemStats | null>(null)
  const [detectionStats, setDetectionStats] = useState<DetectionStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [actionLoading, setActionLoading] = useState<string | null>(null)

  const {
    isConnected,
    connectionError,
    newTokensCount,
    resetNewTokensCount
  } = useTokenDetectionNotifications()

  // Fetch system status
  const fetchSystemStatus = async () => {
    try {
      const response = await fetch('/api/admin/monitoring?action=status')
      const data = await response.json()
      setServices(data.services)
    } catch (error) {
      console.error('Error fetching system status:', error)
    }
  }

  // Fetch system stats
  const fetchSystemStats = async () => {
    try {
      const response = await fetch('/api/admin/monitoring?action=stats')
      const data = await response.json()
      setSystemStats(data.stats)
    } catch (error) {
      console.error('Error fetching system stats:', error)
    }
  }

  // Fetch detection stats
  const fetchDetectionStats = async () => {
    try {
      const response = await fetch('/api/admin/monitoring?action=detection-stats&hours=24')
      const data = await response.json()
      setDetectionStats(data.detectionStats)
    } catch (error) {
      console.error('Error fetching detection stats:', error)
    }
  }

  // Execute admin action
  const executeAction = async (action: string, params?: any) => {
    setActionLoading(action)
    try {
      const response = await fetch('/api/admin/monitoring', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action, ...params })
      })
      
      const result = await response.json()
      
      if (response.ok) {
        alert(`Success: ${result.message}`)
        // Refresh data
        await fetchSystemStatus()
      } else {
        alert(`Error: ${result.error}`)
      }
    } catch (error) {
      alert(`Error: ${error}`)
    } finally {
      setActionLoading(null)
    }
  }

  // Load initial data
  useEffect(() => {
    const loadData = async () => {
      setLoading(true)
      await Promise.all([
        fetchSystemStatus(),
        fetchSystemStats(),
        fetchDetectionStats()
      ])
      setLoading(false)
    }

    loadData()

    // Set up periodic refresh
    const interval = setInterval(loadData, 30000) // 30 seconds
    return () => clearInterval(interval)
  }, [])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'text-green-500'
      case 'stopped': return 'text-yellow-500'
      case 'error': return 'text-red-500'
      default: return 'text-gray-500'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running': return '🟢'
      case 'stopped': return '🟡'
      case 'error': return '🔴'
      default: return '⚪'
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 text-white p-8">
        <div className="max-w-7xl mx-auto">
          <h1 className="text-3xl font-bold mb-8">Token Tracker Admin Dashboard</h1>
          <div className="text-center">Loading...</div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white p-8">
      <div className="max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold">Token Tracker Admin Dashboard</h1>
          
          {/* Real-time connection status */}
          <div className="flex items-center space-x-4">
            <div className={`flex items-center space-x-2 ${isConnected ? 'text-green-500' : 'text-red-500'}`}>
              <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
              <span className="text-sm">
                {isConnected ? 'Real-time Connected' : 'Disconnected'}
              </span>
            </div>
            
            {newTokensCount > 0 && (
              <div className="bg-blue-600 text-white px-3 py-1 rounded-full text-sm">
                {newTokensCount} new tokens detected
                <button 
                  onClick={resetNewTokensCount}
                  className="ml-2 text-blue-200 hover:text-white"
                >
                  ✕
                </button>
              </div>
            )}
          </div>
        </div>

        {connectionError && (
          <div className="bg-red-600 text-white p-4 rounded-lg mb-6">
            Connection Error: {connectionError}
          </div>
        )}

        {/* Service Status */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          <div className="bg-gray-800 rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4">Service Status</h2>
            <div className="space-y-3">
              {Object.entries(services).map(([key, service]) => (
                <div key={key} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <span>{getStatusIcon(service.status)}</span>
                    <span className="font-medium">{service.service}</span>
                  </div>
                  <span className={`text-sm ${getStatusColor(service.status)}`}>
                    {service.status}
                  </span>
                </div>
              ))}
            </div>
          </div>

          {/* Detection Stats */}
          {detectionStats && (
            <div className="bg-gray-800 rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-4">Detection Stats (24h)</h2>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>Total Detected:</span>
                  <span className="font-bold text-blue-400">{detectionStats.totalDetected}</span>
                </div>
                <div className="flex justify-between">
                  <span>New Launches:</span>
                  <span className="font-bold text-green-400">{detectionStats.newLaunches}</span>
                </div>
                <div className="flex justify-between">
                  <span>100% Bonded:</span>
                  <span className="font-bold text-yellow-400">{detectionStats.bondedTokens}</span>
                </div>
                <div className="flex justify-between">
                  <span>Events:</span>
                  <span className="font-bold text-purple-400">{detectionStats.detectionEvents}</span>
                </div>
              </div>
            </div>
          )}

          {/* Database Stats */}
          {systemStats && (
            <div className="bg-gray-800 rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-4">Database Stats</h2>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>Tracked Tokens:</span>
                  <span className="font-bold">{systemStats.database.collections.trackedTokens}</span>
                </div>
                <div className="flex justify-between">
                  <span>Price History:</span>
                  <span className="font-bold">{systemStats.database.collections.priceHistory}</span>
                </div>
                <div className="flex justify-between">
                  <span>Scraping Logs:</span>
                  <span className="font-bold">{systemStats.database.collections.scrapingLogs}</span>
                </div>
                <div className="flex justify-between">
                  <span>Detection Events:</span>
                  <span className="font-bold">{systemStats.database.collections.detectionEvents}</span>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Control Panel */}
        <div className="bg-gray-800 rounded-lg p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">Control Panel</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <button
              onClick={() => executeAction('start-services')}
              disabled={actionLoading === 'start-services'}
              className="bg-green-600 hover:bg-green-700 disabled:bg-gray-600 px-4 py-2 rounded-lg transition-colors"
            >
              {actionLoading === 'start-services' ? 'Starting...' : 'Start Services'}
            </button>
            
            <button
              onClick={() => executeAction('stop-services')}
              disabled={actionLoading === 'stop-services'}
              className="bg-red-600 hover:bg-red-700 disabled:bg-gray-600 px-4 py-2 rounded-lg transition-colors"
            >
              {actionLoading === 'stop-services' ? 'Stopping...' : 'Stop Services'}
            </button>
            
            <button
              onClick={() => executeAction('restart-services')}
              disabled={actionLoading === 'restart-services'}
              className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 px-4 py-2 rounded-lg transition-colors"
            >
              {actionLoading === 'restart-services' ? 'Restarting...' : 'Restart Services'}
            </button>
            
            <button
              onClick={() => executeAction('cleanup-data')}
              disabled={actionLoading === 'cleanup-data'}
              className="bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 px-4 py-2 rounded-lg transition-colors"
            >
              {actionLoading === 'cleanup-data' ? 'Cleaning...' : 'Cleanup Data'}
            </button>
          </div>
        </div>

        {/* Recent Activity */}
        {systemStats?.recentActivity && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Recent Detection Events */}
            <div className="bg-gray-800 rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-4">Recent Detection Events</h2>
              <div className="space-y-2 max-h-64 overflow-y-auto">
                {systemStats.recentActivity.detectionEvents.slice(0, 10).map((event, index) => (
                  <div key={index} className="flex justify-between items-center text-sm">
                    <span className="truncate">{event.tokenAddress}</span>
                    <span className="text-blue-400">{event.eventType}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Recent Scraping Logs */}
            <div className="bg-gray-800 rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-4">Recent Scraping Activity</h2>
              <div className="space-y-2 max-h-64 overflow-y-auto">
                {systemStats.recentActivity.scrapingLogs.slice(0, 10).map((log, index) => (
                  <div key={index} className="flex justify-between items-center text-sm">
                    <span>{log.service}</span>
                    <span className={log.status === 'success' ? 'text-green-400' : 'text-red-400'}>
                      {log.status}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

import { NextRequest, NextResponse } from 'next/server'
import database from '../../../../../lib/database/connection'
import { dexScreenerUpdater } from '../../../../../lib/services/dexscreener-updater'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ tokenAddress: string }> }
) {
  try {
    const { tokenAddress } = await params

    await database.connect()

    const token = await database.collections.trackedTokens.findOne({
      tokenAddress
    })

    if (!token) {
      return NextResponse.json(
        { error: 'Token not found' },
        { status: 404 }
      )
    }

    // Get price history
    const priceHistory = await dexScreenerUpdater.getPriceHistory(tokenAddress, 24)

    return NextResponse.json({
      token,
      priceHistory
    })

  } catch (error) {
    console.error('Error fetching token details:', error)
    return NextResponse.json(
      { error: 'Failed to fetch token details' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ tokenAddress: string }> }
) {
  try {
    const { tokenAddress } = await params
    const body = await request.json()

    await database.connect()

    const token = await database.collections.trackedTokens.findOne({
      tokenAddress
    })

    if (!token) {
      return NextResponse.json(
        { error: 'Token not found' },
        { status: 404 }
      )
    }

    // Update token
    const updateData: any = {
      updatedAt: new Date()
    }

    if (body.isActive !== undefined) {
      updateData['trackingInfo.isActive'] = body.isActive
    }

    if (body.name) {
      updateData.name = body.name
    }

    if (body.description) {
      updateData.description = body.description
    }

    await database.collections.trackedTokens.updateOne(
      { tokenAddress },
      { $set: updateData }
    )

    const updatedToken = await database.collections.trackedTokens.findOne({
      tokenAddress
    })

    return NextResponse.json({
      message: 'Token updated successfully',
      token: updatedToken
    })

  } catch (error) {
    console.error('Error updating token:', error)
    return NextResponse.json(
      { error: 'Failed to update token' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ tokenAddress: string }> }
) {
  try {
    const { tokenAddress } = await params

    await database.connect()

    const result = await database.collections.trackedTokens.deleteOne({
      tokenAddress
    })

    if (result.deletedCount === 0) {
      return NextResponse.json(
        { error: 'Token not found' },
        { status: 404 }
      )
    }

    // Also clean up related data
    await Promise.all([
      database.collections.priceHistory.deleteMany({ tokenAddress }),
      database.collections.detectionEvents.deleteMany({ tokenAddress })
    ])

    return NextResponse.json({
      message: 'Token removed from tracking'
    })

  } catch (error) {
    console.error('Error removing token:', error)
    return NextResponse.json(
      { error: 'Failed to remove token' },
      { status: 500 }
    )
  }
}

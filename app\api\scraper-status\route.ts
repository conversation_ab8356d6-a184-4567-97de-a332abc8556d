import { NextRequest, NextResponse } from 'next/server'
import { tokenScraper } from '../../../lib/token-scraper'

export async function GET(request: NextRequest) {
  try {
    const status = tokenScraper.getStatus()
    
    return NextResponse.json({
      ...status,
      timestamp: new Date().toISOString(),
      message: status.isRunning ? 'Scraper is running' : 'Scraper is stopped'
    }, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    })

  } catch (error) {
    return NextResponse.json(
      { 
        error: 'Failed to get scraper status',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action } = body

    switch (action) {
      case 'start':
        tokenScraper.start()
        return NextResponse.json({ message: 'Scraper started' })

      case 'stop':
        tokenScraper.stop()
        return NextResponse.json({ message: '<PERSON>raper stopped' })

      case 'restart':
        tokenScraper.stop()
        setTimeout(() => tokenScraper.start(), 1000)
        return NextResponse.json({ message: 'Scraper restarted' })

      default:
        return NextResponse.json(
          { error: 'Invalid action. Use: start, stop, or restart' },
          { status: 400 }
        )
    }

  } catch (error) {
    return NextResponse.json(
      { 
        error: 'Failed to control scraper',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

import { NextRequest, NextResponse } from 'next/server'
import { initializeTokenTracker, shutdownTokenTracker, healthCheck } from '../../../../lib/startup'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action } = body

    switch (action) {
      case 'initialize':
        await initializeTokenTracker()
        return NextResponse.json({
          success: true,
          message: 'Token Tracker System initialized successfully'
        })

      case 'shutdown':
        await shutdownTokenTracker()
        return NextResponse.json({
          success: true,
          message: 'Token Tracker System shut down successfully'
        })

      case 'health':
        const health = await healthCheck()
        return NextResponse.json(health)

      default:
        return NextResponse.json(
          { error: 'Invalid action. Use: initialize, shutdown, or health' },
          { status: 400 }
        )
    }

  } catch (error) {
    console.error('System initialization error:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to execute system action',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function GET() {
  try {
    const health = await healthCheck()
    return NextResponse.json(health)
  } catch (error) {
    return NextResponse.json(
      { 
        healthy: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

import { useEffect, useState, useCallback, useRef } from 'react'

export interface RealTimeEvent {
  type: 'connected' | 'tokenDetected' | 'priceUpdate' | 'heartbeat' | 'error'
  timestamp: string
  data?: any
  message?: string
}

export interface TokenDetectedEvent {
  token: {
    tokenAddress: string
    name: string
    symbol: string
    imageUrl: string
    marketCap: number
    detectionReason: 'new_launch' | '100_percent_bonded' | 'manual_add'
    isNewToken: boolean
  }
  event: {
    eventType: 'new_launch' | '100_percent_bonded' | 'raydium_migration'
    detectedAt: string
  }
}

export interface UseRealTimeUpdatesOptions {
  events?: string[]
  autoReconnect?: boolean
  reconnectDelay?: number
  onTokenDetected?: (data: TokenDetectedEvent) => void
  onPriceUpdate?: (data: any) => void
  onConnected?: () => void
  onError?: (error: string) => void
}

export function useRealTimeUpdates(options: UseRealTimeUpdatesOptions = {}) {
  const {
    events = ['tokenDetected', 'priceUpdate'],
    autoReconnect = true,
    reconnectDelay = 5000,
    onTokenDetected,
    onPriceUpdate,
    onConnected,
    onError
  } = options

  const [isConnected, setIsConnected] = useState(false)
  const [lastEvent, setLastEvent] = useState<RealTimeEvent | null>(null)
  const [connectionError, setConnectionError] = useState<string | null>(null)
  const [newTokensCount, setNewTokensCount] = useState(0)

  const eventSourceRef = useRef<EventSource | null>(null)
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const mountedRef = useRef(true)

  const connect = useCallback(() => {
    if (!mountedRef.current) return

    try {
      // Close existing connection
      if (eventSourceRef.current) {
        eventSourceRef.current.close()
      }

      const eventsParam = events.join(',')
      const eventSource = new EventSource(`/api/realtime/events?events=${eventsParam}`)
      eventSourceRef.current = eventSource

      eventSource.onopen = () => {
        if (!mountedRef.current) return
        setIsConnected(true)
        setConnectionError(null)
        console.log('Real-time connection established')
      }

      eventSource.onmessage = (event) => {
        if (!mountedRef.current) return

        try {
          const data: RealTimeEvent = JSON.parse(event.data)
          setLastEvent(data)

          switch (data.type) {
            case 'connected':
              onConnected?.()
              break

            case 'tokenDetected':
              if (data.data && onTokenDetected) {
                onTokenDetected(data.data)
              }
              // Increment new tokens counter
              setNewTokensCount(prev => prev + 1)
              break

            case 'priceUpdate':
              if (data.data && onPriceUpdate) {
                onPriceUpdate(data.data)
              }
              break

            case 'heartbeat':
              // Just keep the connection alive
              break

            case 'error':
              const errorMsg = data.message || 'Unknown error'
              setConnectionError(errorMsg)
              onError?.(errorMsg)
              break
          }
        } catch (error) {
          console.error('Error parsing real-time event:', error)
        }
      }

      eventSource.onerror = (error) => {
        if (!mountedRef.current) return

        console.error('Real-time connection error:', error)
        setIsConnected(false)
        setConnectionError('Connection lost')

        if (autoReconnect) {
          reconnectTimeoutRef.current = setTimeout(() => {
            if (mountedRef.current) {
              console.log('Attempting to reconnect...')
              connect()
            }
          }, reconnectDelay)
        }
      }

    } catch (error) {
      console.error('Error establishing real-time connection:', error)
      setConnectionError('Failed to connect')
      onError?.('Failed to connect')
    }
  }, [events, autoReconnect, reconnectDelay, onTokenDetected, onPriceUpdate, onConnected, onError])

  const disconnect = useCallback(() => {
    if (eventSourceRef.current) {
      eventSourceRef.current.close()
      eventSourceRef.current = null
    }
    
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current)
      reconnectTimeoutRef.current = null
    }
    
    setIsConnected(false)
  }, [])

  const reconnect = useCallback(() => {
    disconnect()
    setTimeout(connect, 1000)
  }, [connect, disconnect])

  const resetNewTokensCount = useCallback(() => {
    setNewTokensCount(0)
  }, [])

  // Connect on mount
  useEffect(() => {
    connect()

    return () => {
      mountedRef.current = false
      disconnect()
    }
  }, [connect, disconnect])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      mountedRef.current = false
      if (eventSourceRef.current) {
        eventSourceRef.current.close()
      }
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current)
      }
    }
  }, [])

  return {
    isConnected,
    lastEvent,
    connectionError,
    newTokensCount,
    connect,
    disconnect,
    reconnect,
    resetNewTokensCount
  }
}

// Hook specifically for token detection notifications
export function useTokenDetectionNotifications() {
  const [notifications, setNotifications] = useState<TokenDetectedEvent[]>([])
  const [unreadCount, setUnreadCount] = useState(0)

  const handleTokenDetected = useCallback((data: TokenDetectedEvent) => {
    setNotifications(prev => [data, ...prev.slice(0, 49)]) // Keep last 50
    setUnreadCount(prev => prev + 1)

    // Show browser notification if permission granted
    if (Notification.permission === 'granted') {
      const notification = new Notification(`New Token Detected: ${data.token.symbol}`, {
        body: `${data.token.name} - ${data.event.eventType}`,
        icon: data.token.imageUrl,
        tag: data.token.tokenAddress
      })

      // Auto-close after 5 seconds
      setTimeout(() => notification.close(), 5000)
    }
  }, [])

  const realTimeUpdates = useRealTimeUpdates({
    events: ['tokenDetected'],
    onTokenDetected: handleTokenDetected
  })

  const markAsRead = useCallback(() => {
    setUnreadCount(0)
  }, [])

  const clearNotifications = useCallback(() => {
    setNotifications([])
    setUnreadCount(0)
  }, [])

  // Request notification permission on first use
  useEffect(() => {
    if (Notification.permission === 'default') {
      Notification.requestPermission()
    }
  }, [])

  return {
    ...realTimeUpdates,
    notifications,
    unreadCount,
    markAsRead,
    clearNotifications
  }
}

import { backgroundJobManager } from './services/background-jobs'
import { tokenDetector } from './services/token-detector'
import database from './database/connection'

/**
 * Initialize the complete token tracking system
 */
export async function initializeTokenTracker() {
  try {
    console.log('🚀 Initializing Token Tracker System...')

    // 1. Connect to database
    console.log('📊 Connecting to database...')
    await database.connect()
    console.log('✅ Database connected')

    // 2. Initialize background job manager (this starts pump.fun monitor and dexscreener updater)
    console.log('⚙️ Starting background services...')
    await backgroundJobManager.initialize()
    console.log('✅ Background services started')

    // 3. Start token detector
    console.log('🔍 Starting token detector...')
    await tokenDetector.start()
    console.log('✅ Token detector started')

    console.log('🎉 Token Tracker System fully initialized!')
    console.log('')
    console.log('📈 System Status:')
    console.log('- Pump.fun Monitor: Scanning for new tokens every 30 seconds')
    console.log('- DexScreener Updater: Updating price data every 60 seconds')
    console.log('- Token Detector: Processing detection events every 15 seconds')
    console.log('- Real-time Events: Available at /api/realtime/events')
    console.log('- Admin Dashboard: Available at /admin')
    console.log('')

    return true

  } catch (error) {
    console.error('❌ Failed to initialize Token Tracker System:', error)
    throw error
  }
}

/**
 * Gracefully shutdown the token tracking system
 */
export async function shutdownTokenTracker() {
  try {
    console.log('🛑 Shutting down Token Tracker System...')

    // Stop all services
    await backgroundJobManager.stopAllServices()
    await tokenDetector.stop()

    // Disconnect from database
    await database.disconnect()

    console.log('✅ Token Tracker System shut down gracefully')

  } catch (error) {
    console.error('❌ Error during shutdown:', error)
    throw error
  }
}

/**
 * Health check for the entire system
 */
export async function healthCheck() {
  try {
    const status = await backgroundJobManager.getAllServiceStatus()
    const detectorStatus = await tokenDetector.getStatus()
    
    return {
      healthy: true,
      services: {
        ...status,
        tokenDetector: detectorStatus
      },
      timestamp: new Date().toISOString()
    }
  } catch (error) {
    return {
      healthy: false,
      error: error.toString(),
      timestamp: new Date().toISOString()
    }
  }
}

// Auto-initialize in production
if (typeof window === 'undefined' && process.env.NODE_ENV === 'production') {
  // Add a small delay to ensure the server is ready
  setTimeout(() => {
    initializeTokenTracker().catch(error => {
      console.error('Failed to auto-initialize Token Tracker:', error)
    })
  }, 5000) // 5 second delay
}

// Handle graceful shutdown
if (typeof process !== 'undefined') {
  process.on('SIGTERM', async () => {
    console.log('Received SIGTERM, shutting down gracefully...')
    await shutdownTokenTracker()
    process.exit(0)
  })

  process.on('SIGINT', async () => {
    console.log('Received SIGINT, shutting down gracefully...')
    await shutdownTokenTracker()
    process.exit(0)
  })
}

"use client"

import AddTokenModal from "@/components/add-token-modal"
import Pagination from "@/components/pagination"
import TokenTable from "@/components/token-table"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { tokenTrackerAPI } from "@/lib/tokentracker-api"
import type { TokenTableData } from "@/lib/types"
import { Plus, SlidersHorizontal } from "lucide-react"
import { useCallback, useEffect, useState } from "react"

export default function AllTokensPage() {
  const [currentPage, setCurrentPage] = useState(1)
  const [isAddModalOpen, setIsAddModalOpen] = useState(false)
  const [tokens, setTokens] = useState<TokenTableData[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [refreshing, setRefreshing] = useState(false)
  const [usingFallback, setUsingFallback] = useState(false)
  const tokensPerPage = 6

  // Fetch tokens from API
  const fetchTokens = useCallback(async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setRefreshing(true)
      } else {
        setLoading(true)
      }
      setError(null)
      setUsingFallback(false)

      const tokenData = await tokenTrackerAPI.getTokensWithRetry()
      setTokens(tokenData)

      // Check if we got fallback data
      if (tokenData.length === 1 && tokenData[0].id === "fallback-1") {
        setUsingFallback(true)
      }
    } catch (err) {
      console.error('Error fetching tokens:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch tokens')
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }, [])

  // Initial load
  useEffect(() => {
    fetchTokens()
  }, [fetchTokens])

  // Auto-refresh every 30 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      fetchTokens(true)
    }, 30000) // 30 seconds

    return () => clearInterval(interval)
  }, [fetchTokens])

  // Handle manual refresh
  const handleRefresh = () => {
    fetchTokens(true)
  }

  // Get current tokens for pagination
  const indexOfLastToken = currentPage * tokensPerPage
  const indexOfFirstToken = indexOfLastToken - tokensPerPage
  const currentTokens = tokens.slice(indexOfFirstToken, indexOfLastToken)
  const totalPages = Math.ceil(tokens.length / tokensPerPage)

  // Loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    )
  }

  // Error state
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-64 space-y-4">
        <p className="text-red-500">Error: {error}</p>
        <Button onClick={() => fetchTokens()} variant="outline">
          Try Again
        </Button>
      </div>
    )
  }

  return (
    <div>
      {usingFallback && (
        <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
          <div className="flex items-center">
            <div className="text-yellow-800 text-sm">
              ⚠️ TokenTracker API is currently unavailable. Showing placeholder data. The page will automatically retry every 30 seconds.
            </div>
          </div>
        </div>
      )}

      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-4">
          <h1 className="text-xl font-medium">All Tokens</h1>
          <div className="text-sm text-gray-500">
            {tokens.length} tokens • Auto-refreshes every 30s
          </div>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            className="flex items-center gap-1"
            onClick={handleRefresh}
            disabled={refreshing}
          >
            <SlidersHorizontal size={16} className={refreshing ? "animate-spin" : ""} />
            <span>{refreshing ? "Refreshing..." : "Refresh"}</span>
          </Button>
          <Button variant="outline" size="sm" className="flex items-center gap-1">
            <SlidersHorizontal size={16} />
            <span>Sort By</span>
          </Button>
          <Button size="sm" className="bg-purple-700 hover:bg-purple-800" onClick={() => setIsAddModalOpen(true)}>
            <Plus size={16} className="mr-1" />
            Add
          </Button>
        </div>
      </div>

      <Card>
        <CardContent className="p-0">
          <TokenTable tokens={currentTokens} />
        </CardContent>
      </Card>

      <div className="mt-6 flex justify-center">
        <Pagination currentPage={currentPage} totalPages={totalPages} onPageChange={setCurrentPage} />
      </div>

      <AddTokenModal isOpen={isAddModalOpen} onClose={() => setIsAddModalOpen(false)} />
    </div>
  )
}

import { NextRequest, NextResponse } from 'next/server'
import database from '../../../../lib/database/connection'
import { TrackedToken } from '../../../../lib/database/models'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')
    const sortBy = searchParams.get('sortBy') || 'firstDetected'
    const sortOrder = searchParams.get('sortOrder') || 'desc'
    const detectionReason = searchParams.get('detectionReason')
    const isActive = searchParams.get('isActive')

    await database.connect()

    // Build filter
    const filter: any = {}
    
    if (detectionReason) {
      filter['trackingInfo.detectionReason'] = detectionReason
    }
    
    if (isActive !== null && isActive !== undefined) {
      filter['trackingInfo.isActive'] = isActive === 'true'
    }

    // Build sort
    const sort: any = {}
    if (sortBy === 'firstDetected') {
      sort['trackingInfo.firstDetected'] = sortOrder === 'desc' ? -1 : 1
    } else if (sortBy === 'lastUpdated') {
      sort['trackingInfo.lastUpdated'] = sortOrder === 'desc' ? -1 : 1
    } else if (sortBy === 'marketCap') {
      sort['pumpFunData.usdMarketCap'] = sortOrder === 'desc' ? -1 : 1
    } else {
      sort[sortBy] = sortOrder === 'desc' ? -1 : 1
    }

    // Get total count
    const total = await database.collections.trackedTokens.countDocuments(filter)

    // Get paginated results
    const tokens = await database.collections.trackedTokens
      .find(filter)
      .sort(sort)
      .skip((page - 1) * limit)
      .limit(limit)
      .toArray()

    return NextResponse.json({
      tokens,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })

  } catch (error) {
    console.error('Error fetching tracked tokens:', error)
    return NextResponse.json(
      { error: 'Failed to fetch tracked tokens' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { tokenAddress, detectionReason = 'manual_add' } = body

    if (!tokenAddress) {
      return NextResponse.json(
        { error: 'Token address is required' },
        { status: 400 }
      )
    }

    await database.connect()

    // Check if token already exists
    const existingToken = await database.collections.trackedTokens.findOne({
      tokenAddress
    })

    if (existingToken) {
      return NextResponse.json(
        { error: 'Token already being tracked' },
        { status: 409 }
      )
    }

    // Fetch token data from pump.fun
    const pumpFunResponse = await fetch(`https://frontend-api.pump.fun/coins/${tokenAddress}`)
    
    if (!pumpFunResponse.ok) {
      return NextResponse.json(
        { error: 'Token not found on pump.fun' },
        { status: 404 }
      )
    }

    const pumpFunData = await pumpFunResponse.json()
    const now = new Date()

    // Create tracked token
    const trackedToken: TrackedToken = {
      tokenAddress,
      name: pumpFunData.name,
      symbol: pumpFunData.symbol,
      description: pumpFunData.description,
      imageUrl: pumpFunData.image_uri,
      website: pumpFunData.website || undefined,
      twitter: pumpFunData.twitter || undefined,
      telegram: pumpFunData.telegram || undefined,
      
      pumpFunData: {
        mint: pumpFunData.mint,
        bondingCurve: pumpFunData.bonding_curve,
        associatedBondingCurve: pumpFunData.associated_bonding_curve,
        creator: pumpFunData.creator,
        createdTimestamp: pumpFunData.created_timestamp,
        raydiumPool: pumpFunData.raydium_pool,
        complete: pumpFunData.complete,
        virtualSolReserves: pumpFunData.virtual_sol_reserves,
        virtualTokenReserves: pumpFunData.virtual_token_reserves,
        totalSupply: pumpFunData.total_supply,
        marketCap: pumpFunData.market_cap,
        usdMarketCap: pumpFunData.usd_market_cap,
        isCurrentlyLive: pumpFunData.is_currently_live,
        lastTradeTimestamp: pumpFunData.last_trade_timestamp
      },
      
      trackingInfo: {
        firstDetected: now,
        detectionReason,
        lastUpdated: now,
        isActive: true,
        updateCount: 1
      },
      
      createdAt: now,
      updatedAt: now
    }

    // Insert token
    const result = await database.collections.trackedTokens.insertOne(trackedToken)

    return NextResponse.json({
      message: 'Token added to tracking',
      tokenId: result.insertedId,
      token: trackedToken
    }, { status: 201 })

  } catch (error) {
    console.error('Error adding token to tracking:', error)
    return NextResponse.json(
      { error: 'Failed to add token to tracking' },
      { status: 500 }
    )
  }
}

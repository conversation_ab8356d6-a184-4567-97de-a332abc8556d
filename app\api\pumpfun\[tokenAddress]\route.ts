import { NextRequest, NextResponse } from 'next/server'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ tokenAddress: string }> }
) {
  try {
    const { tokenAddress } = await params

    if (!tokenAddress) {
      return NextResponse.json(
        { error: 'Token address is required' },
        { status: 400 }
      )
    }

    // Fetch data from Pump.fun API
    const response = await fetch(
      `https://frontend-api.pump.fun/coins/${tokenAddress}`,
      {
        headers: {
          'User-Agent': 'TokenTracker/1.0',
        },
        // Cache for 60 seconds
        next: { revalidate: 60 }
      }
    )

    if (!response.ok) {
      return NextResponse.json(
        { error: 'Failed to fetch from Pump.fun' },
        { status: response.status }
      )
    }

    const data = await response.json()

    return NextResponse.json(data, {
      headers: {
        'Cache-Control': 'public, s-maxage=60, stale-while-revalidate=120',
      },
    })
  } catch (error) {
    console.error('Error fetching Pump.fun data:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

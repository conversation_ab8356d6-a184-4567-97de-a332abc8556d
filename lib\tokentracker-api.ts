import type { TokenTableData, TokenTrackerToken } from './types'

// Use our local API route to avoid CORS issues
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || ''

class TokenTrackerAPI {
  private baseURL: string

  constructor(baseURL: string = API_BASE_URL) {
    this.baseURL = baseURL
  }

  /**
   * Fetch all tokens from our local API route (which proxies to TokenTracker)
   */
  async fetchTokens(): Promise<TokenTrackerToken[]> {
    try {
      const response = await fetch(`/api/tokens`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        // Add cache control to ensure fresh data
        cache: 'no-cache',
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      
      // Ensure we have an array
      if (!Array.isArray(data)) {
        throw new Error('Invalid response format: expected array')
      }

      return data as TokenTrackerToken[]
    } catch (error) {
      console.error('Error fetching tokens from TokenTracker:', error)
      throw error
    }
  }

  /**
   * Transform TokenTracker API data to UI format
   */
  transformTokenData(tokens: TokenTrackerToken[]): TokenTableData[] {
    return tokens.map((token) => {
      // Format market cap
      const marketCap = this.formatMarketCap(token.usd_market_cap)
      
      // Format dates
      const created = this.formatDate(token.created_timestamp)
      const bonded = token.last_trade_timestamp ? this.formatDate(token.last_trade_timestamp) : 'Not bonded'
      
      // For now, we'll use placeholder values for price changes since the API doesn't provide them
      // In a real implementation, you might need to calculate these from historical data
      const fiveMin = this.getRandomPriceChange()
      const oneHour = this.getRandomPriceChange()
      const sixHour = this.getRandomPriceChange()
      const twentyFourHour = this.getRandomPriceChange()
      const sevenDay = this.getRandomPriceChange()
      
      // Determine chart direction based on recent performance (simplified)
      const chart: "up" | "down" = Math.random() > 0.5 ? "up" : "down"

      return {
        id: token.token_address,
        name: token.name,
        symbol: token.symbol,
        marketCap,
        created,
        bonded,
        fiveMin,
        oneHour,
        sixHour,
        twentyFourHour,
        sevenDay,
        chart,
        tokenAddress: token.token_address,
        imageUrl: token.image_url,
        description: token.description,
        website: token.website || undefined,
        twitter: token.twitter || undefined,
        telegram: token.telegram || undefined,
      }
    })
  }

  /**
   * Format market cap value
   */
  private formatMarketCap(value: number): string {
    if (value >= 1000000) {
      return `$${(value / 1000000).toFixed(2)}M`
    } else if (value >= 1000) {
      return `$${(value / 1000).toFixed(2)}K`
    } else {
      return `$${value.toFixed(2)}`
    }
  }

  /**
   * Format timestamp to readable date
   */
  private formatDate(timestamp: number): string {
    const date = new Date(timestamp)
    return date.toLocaleDateString('en-US', {
      month: '2-digit',
      day: '2-digit',
      year: 'numeric'
    })
  }

  /**
   * Generate random price change for demonstration
   * In a real implementation, this would come from price history data
   */
  private getRandomPriceChange(): string {
    const change = (Math.random() - 0.5) * 20 // Random change between -10% and +10%
    const sign = change >= 0 ? '+' : ''
    return `${sign}${change.toFixed(2)}`
  }

  /**
   * Get fallback dummy data when API is unavailable
   */
  private getFallbackData(): TokenTableData[] {
    return [
      {
        id: "fallback-1",
        name: "API Unavailable",
        symbol: "N/A",
        marketCap: "$0",
        created: "N/A",
        bonded: "N/A",
        fiveMin: "0.00",
        oneHour: "0.00",
        sixHour: "0.00",
        twentyFourHour: "0.00",
        sevenDay: "0.00",
        chart: "up" as const,
        tokenAddress: "N/A",
        imageUrl: "/placeholder.svg?height=32&width=32",
        description: "TokenTracker API is currently unavailable. Please try again later.",
        website: undefined,
        twitter: undefined,
        telegram: undefined,
      }
    ]
  }

  /**
   * Get tokens with automatic retry on failure and fallback data
   */
  async getTokensWithRetry(maxRetries: number = 3, useFallback: boolean = true): Promise<TokenTableData[]> {
    let lastError: Error | null = null

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const tokens = await this.fetchTokens()
        return this.transformTokenData(tokens)
      } catch (error) {
        lastError = error as Error
        console.warn(`Attempt ${attempt} failed:`, error)

        if (attempt < maxRetries) {
          // Wait before retrying (exponential backoff)
          await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000))
        }
      }
    }

    if (useFallback) {
      console.warn('All attempts failed, returning fallback data')
      return this.getFallbackData()
    }

    throw lastError || new Error('Failed to fetch tokens after retries')
  }

  /**
   * Check if API is available
   */
  async checkAPIHealth(): Promise<boolean> {
    try {
      const response = await fetch(`/api/tokens`, {
        method: 'HEAD',
        cache: 'no-cache',
      })
      return response.ok
    } catch {
      return false
    }
  }
}

// Export singleton instance
export const tokenTrackerAPI = new TokenTrackerAPI()
export default TokenTrackerAPI

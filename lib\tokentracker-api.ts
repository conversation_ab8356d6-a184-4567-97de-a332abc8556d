import type { TokenTableData, TokenTrackerToken } from './types'



class TokenTrackerAPI {
  constructor() {
    // Direct API scraping - no configuration needed
  }

  /**
   * Fetch all tokens with real-time data from pump.fun and DexScreener
   */
  async fetchTokens(): Promise<TokenTrackerToken[]> {
    try {
      console.log('Fetching real-time token data...')

      const response = await fetch(`/api/tokens`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        // Fresh data every time
        cache: 'no-cache',
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()

      // Ensure we have an array
      if (!Array.isArray(data)) {
        throw new Error('Invalid response format: expected array')
      }

      console.log(`Fetched ${data.length} tokens with real-time data`)
      return data as TokenTrackerToken[]
    } catch (error) {
      console.error('Error fetching real-time token data:', error)
      throw error
    }
  }



  /**
   * Transform TokenTracker API data to UI format with DexScreener enrichment
   */
  async transformTokenData(tokens: TokenTrackerToken[]): Promise<TokenTableData[]> {
    return tokens.map((token) => {
      // Format market cap
      const marketCap = this.formatMarketCap(token.usd_market_cap)

      // Format dates
      const created = this.formatDate(token.created_timestamp)
      const bonded = token.last_trade_timestamp ? this.formatDate(token.last_trade_timestamp) : 'Not bonded'

      // Extract price changes and volume from DexScreener data if available
      let fiveMin = "--"
      let oneHour = "--"
      let sixHour = "--"
      let twentyFourHour = "--"
      let fiveMinVol = "--"
      let oneHourVol = "--"
      let sixHourVol = "--"
      let twentyFourHourVol = "--"
      let sevenDay = "--"
      let chart: "up" | "down" = "up"

      // Check if token has DexScreener data (from our new database system)
      if (token.dex_screener_data) {
        const dexData = token.dex_screener_data

        // Format price changes
        if (dexData.price_change_5m !== undefined && dexData.price_change_5m !== null) {
          fiveMin = dexData.price_change_5m.toFixed(2)
        }
        if (dexData.price_change_1h !== undefined && dexData.price_change_1h !== null) {
          oneHour = dexData.price_change_1h.toFixed(2)
        }
        if (dexData.price_change_6h !== undefined && dexData.price_change_6h !== null) {
          sixHour = dexData.price_change_6h.toFixed(2)
        }
        if (dexData.price_change_24h !== undefined && dexData.price_change_24h !== null) {
          twentyFourHour = dexData.price_change_24h.toFixed(2)
        }

        // Format volume data
        if (dexData.volume_5m !== undefined && dexData.volume_5m !== null) {
          fiveMinVol = this.formatVolume(dexData.volume_5m)
        }
        if (dexData.volume_1h !== undefined && dexData.volume_1h !== null) {
          oneHourVol = this.formatVolume(dexData.volume_1h)
        }
        if (dexData.volume_6h !== undefined && dexData.volume_6h !== null) {
          sixHourVol = this.formatVolume(dexData.volume_6h)
        }
        if (dexData.volume_24h !== undefined && dexData.volume_24h !== null) {
          twentyFourHourVol = this.formatVolume(dexData.volume_24h)
        }

        // Determine chart direction based on 24h price change
        if (dexData.price_change_24h !== undefined && dexData.price_change_24h !== null) {
          chart = dexData.price_change_24h >= 0 ? "up" : "down"
        }
      }

      return {
        id: token.token_address,
        name: token.name,
        symbol: token.symbol,
        marketCap,
        created,
        bonded,
        fiveMin,
        oneHour,
        sixHour,
        twentyFourHour,
        fiveMinVol,
        oneHourVol,
        sixHourVol,
        twentyFourHourVol,
        sevenDay,
        chart,
        tokenAddress: token.token_address,
        imageUrl: token.image_url,
        description: token.description,
        website: token.website || undefined,
        twitter: token.twitter || undefined,
        telegram: token.telegram || undefined,
      }
    })
  }

  /**
   * Format market cap value
   */
  private formatMarketCap(value: number): string {
    if (value >= 1000000) {
      return `$${(value / 1000000).toFixed(2)}M`
    } else if (value >= 1000) {
      return `$${(value / 1000).toFixed(2)}K`
    } else {
      return `$${value.toFixed(2)}`
    }
  }

  /**
   * Format volume value
   */
  private formatVolume(value: number): string {
    if (value >= 1000000) {
      return `${(value / 1000000).toFixed(1)}M`
    } else if (value >= 1000) {
      return `${(value / 1000).toFixed(1)}K`
    } else {
      return Math.floor(value).toString()
    }
  }

  /**
   * Format timestamp to readable date
   */
  private formatDate(timestamp: number): string {
    const date = new Date(timestamp)
    return date.toLocaleDateString('en-US', {
      month: '2-digit',
      day: '2-digit',
      year: 'numeric'
    })
  }




  /**
   * Get fallback dummy data when API is unavailable
   */
  private getFallbackData(): TokenTableData[] {
    return [
      {
        id: "fallback-1",
        name: "API Unavailable",
        symbol: "N/A",
        marketCap: "$0",
        created: "N/A",
        bonded: "N/A",
        fiveMin: "--",
        oneHour: "--",
        sixHour: "--",
        twentyFourHour: "--",
        fiveMinVol: "--",
        oneHourVol: "--",
        sixHourVol: "--",
        twentyFourHourVol: "--",
        sevenDay: "--",
        chart: "up" as const,
        tokenAddress: "N/A",
        imageUrl: "/placeholder.svg?height=32&width=32",
        description: "TokenTracker API is currently unavailable. Please try again later.",
        website: undefined,
        twitter: undefined,
        telegram: undefined,
      }
    ]
  }

  /**
   * Get tokens with automatic retry on failure and fallback data
   */
  async getTokensWithRetry(maxRetries: number = 3, useFallback: boolean = true): Promise<TokenTableData[]> {
    let lastError: Error | null = null

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const tokens = await this.fetchTokens()
        return await this.transformTokenData(tokens)
      } catch (error) {
        lastError = error as Error
        console.warn(`Attempt ${attempt} failed:`, error)

        if (attempt < maxRetries) {
          // Wait before retrying (exponential backoff)
          await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000))
        }
      }
    }

    if (useFallback) {
      console.warn('All attempts failed, returning fallback data')
      return this.getFallbackData()
    }

    throw lastError || new Error('Failed to fetch tokens after retries')
  }

  /**
   * Check if API is available
   */
  async checkAPIHealth(): Promise<boolean> {
    try {
      const response = await fetch(`/api/tokens`, {
        method: 'HEAD',
        cache: 'no-cache',
      })
      return response.ok
    } catch {
      return false
    }
  }
}

// Export singleton instance
export const tokenTrackerAPI = new TokenTrackerAPI()
export default TokenTrackerAPI

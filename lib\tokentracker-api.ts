import type { TokenTableData, TokenTrackerToken } from './types'

// Use our local API route to avoid CORS issues
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || ''

class TokenTrackerAPI {
  private baseURL: string

  constructor(baseURL: string = API_BASE_URL) {
    this.baseURL = baseURL
  }

  /**
   * Fetch all tokens from our local API route (which proxies to TokenTracker)
   */
  async fetchTokens(): Promise<TokenTrackerToken[]> {
    try {
      const response = await fetch(`/api/tokens`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        // Add cache control to ensure fresh data
        cache: 'no-cache',
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      
      // Ensure we have an array
      if (!Array.isArray(data)) {
        throw new Error('Invalid response format: expected array')
      }

      return data as TokenTrackerToken[]
    } catch (error) {
      console.error('Error fetching tokens from TokenTracker:', error)
      throw error
    }
  }

  /**
   * Transform TokenTracker API data to UI format
   */
  transformTokenData(tokens: TokenTrackerToken[]): TokenTableData[] {
    return tokens.map((token) => {
      // Format market cap
      const marketCap = this.formatMarketCap(token.usd_market_cap)
      
      // Format dates
      const created = this.formatDate(token.created_timestamp)
      const bonded = token.last_trade_timestamp ? this.formatDate(token.last_trade_timestamp) : 'Not bonded'
      
      // The API doesn't provide price change or volume data, showing dashes until we get real data
      const fiveMin = "--"
      const oneHour = "--"
      const sixHour = "--"
      const twentyFourHour = "--"
      const fiveMinVol = "--"
      const oneHourVol = "--"
      const sixHourVol = "--"
      const twentyFourHourVol = "--"
      const sevenDay = "--"

      // Since we don't have price data, we'll use a neutral chart
      const chart: "up" | "down" = "up"

      return {
        id: token.token_address,
        name: token.name,
        symbol: token.symbol,
        marketCap,
        created,
        bonded,
        fiveMin,
        oneHour,
        sixHour,
        twentyFourHour,
        fiveMinVol,
        oneHourVol,
        sixHourVol,
        twentyFourHourVol,
        sevenDay,
        chart,
        tokenAddress: token.token_address,
        imageUrl: token.image_url,
        description: token.description,
        website: token.website || undefined,
        twitter: token.twitter || undefined,
        telegram: token.telegram || undefined,
      }
    })
  }

  /**
   * Format market cap value
   */
  private formatMarketCap(value: number): string {
    if (value >= 1000000) {
      return `$${(value / 1000000).toFixed(2)}M`
    } else if (value >= 1000) {
      return `$${(value / 1000).toFixed(2)}K`
    } else {
      return `$${value.toFixed(2)}`
    }
  }

  /**
   * Format timestamp to readable date
   */
  private formatDate(timestamp: number): string {
    const date = new Date(timestamp)
    return date.toLocaleDateString('en-US', {
      month: '2-digit',
      day: '2-digit',
      year: 'numeric'
    })
  }



  /**
   * Generate realistic price and volume data similar to the original TokenTracker website
   * Most tokens show dashes, but some show real values based on their characteristics
   */
  private generateRealisticPriceVolumeData(token: TokenTrackerToken) {
    // Use token characteristics to determine if it should have data
    const hasRaydiumPool = token.raydium_pool !== null
    const marketCap = token.usd_market_cap
    const tokenAge = Date.now() - token.created_timestamp
    const hasRecentTrade = token.last_trade_timestamp && (Date.now() - token.last_trade_timestamp < 3600000) // 1 hour

    // Higher chance of having data if:
    // - Has Raydium pool (more established)
    // - Higher market cap
    // - Recent trading activity
    // - Token has been around for a while
    const dataChance =
      (hasRaydiumPool ? 0.3 : 0.1) +
      (marketCap > 100000 ? 0.2 : 0.05) +
      (hasRecentTrade ? 0.2 : 0) +
      (tokenAge > 86400000 ? 0.1 : 0) // 24 hours

    const shouldHaveData = Math.random() < dataChance

    if (!shouldHaveData) {
      return {
        fiveMin: "--",
        oneHour: "--",
        sixHour: "--",
        twentyFourHour: "--",
        fiveMinVol: "--",
        oneHourVol: "--",
        sixHourVol: "--",
        twentyFourHourVol: "--",
        sevenDay: "--"
      }
    }

    // Generate realistic values similar to what's seen on the original site
    const generatePriceChange = () => {
      const change = (Math.random() - 0.5) * 40 // -20% to +20%
      return change.toFixed(2)
    }

    const generateVolume = () => {
      const vol = Math.random() * 10000
      if (vol < 1000) {
        return Math.floor(vol).toString()
      } else if (vol < 10000) {
        return (vol / 1000).toFixed(1) + "K"
      } else {
        return (vol / 1000000).toFixed(1) + "M"
      }
    }

    // Randomly assign data to some fields (not all)
    const fields = ['fiveMin', 'oneHour', 'sixHour', 'twentyFourHour', 'fiveMinVol', 'oneHourVol', 'sixHourVol', 'twentyFourHourVol']
    const result: any = {
      fiveMin: "--",
      oneHour: "--",
      sixHour: "--",
      twentyFourHour: "--",
      fiveMinVol: "--",
      oneHourVol: "--",
      sixHourVol: "--",
      twentyFourHourVol: "--",
      sevenDay: "--"
    }

    // Randomly populate 1-3 fields with data
    const numFieldsWithData = Math.floor(Math.random() * 3) + 1
    const shuffledFields = fields.sort(() => Math.random() - 0.5)

    for (let i = 0; i < numFieldsWithData; i++) {
      const field = shuffledFields[i]
      if (field.includes('Vol')) {
        result[field] = generateVolume()
      } else {
        result[field] = generatePriceChange()
      }
    }

    return result
  }

  /**
   * Get fallback dummy data when API is unavailable
   */
  private getFallbackData(): TokenTableData[] {
    return [
      {
        id: "fallback-1",
        name: "API Unavailable",
        symbol: "N/A",
        marketCap: "$0",
        created: "N/A",
        bonded: "N/A",
        fiveMin: "--",
        oneHour: "--",
        sixHour: "--",
        twentyFourHour: "--",
        fiveMinVol: "--",
        oneHourVol: "--",
        sixHourVol: "--",
        twentyFourHourVol: "--",
        sevenDay: "--",
        chart: "up" as const,
        tokenAddress: "N/A",
        imageUrl: "/placeholder.svg?height=32&width=32",
        description: "TokenTracker API is currently unavailable. Please try again later.",
        website: undefined,
        twitter: undefined,
        telegram: undefined,
      }
    ]
  }

  /**
   * Get tokens with automatic retry on failure and fallback data
   */
  async getTokensWithRetry(maxRetries: number = 3, useFallback: boolean = true): Promise<TokenTableData[]> {
    let lastError: Error | null = null

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const tokens = await this.fetchTokens()
        return this.transformTokenData(tokens)
      } catch (error) {
        lastError = error as Error
        console.warn(`Attempt ${attempt} failed:`, error)

        if (attempt < maxRetries) {
          // Wait before retrying (exponential backoff)
          await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000))
        }
      }
    }

    if (useFallback) {
      console.warn('All attempts failed, returning fallback data')
      return this.getFallbackData()
    }

    throw lastError || new Error('Failed to fetch tokens after retries')
  }

  /**
   * Check if API is available
   */
  async checkAPIHealth(): Promise<boolean> {
    try {
      const response = await fetch(`/api/tokens`, {
        method: 'HEAD',
        cache: 'no-cache',
      })
      return response.ok
    } catch {
      return false
    }
  }
}

// Export singleton instance
export const tokenTrackerAPI = new TokenTrackerAPI()
export default TokenTrackerAPI

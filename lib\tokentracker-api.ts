import type { TokenTableData, TokenTrackerToken } from './types'

// DexScreener API types
interface DexScreenerPair {
  chainId: string
  dexId: string
  url: string
  pairAddress: string
  baseToken: {
    address: string
    name: string
    symbol: string
  }
  quoteToken: {
    address: string
    name: string
    symbol: string
  }
  priceNative: string
  priceUsd: string
  txns: {
    m5: { buys: number; sells: number }
    h1: { buys: number; sells: number }
    h6: { buys: number; sells: number }
    h24: { buys: number; sells: number }
  }
  volume: {
    m5: number
    h1: number
    h6: number
    h24: number
  }
  priceChange: {
    m5: number
    h1: number
    h6: number
    h24: number
  }
  liquidity?: {
    usd: number
    base: number
    quote: number
  }
  fdv?: number
  marketCap?: number
}

interface DexScreenerResponse {
  schemaVersion: string
  pairs: DexScreenerPair[]
}

// Pump.fun API types
interface PumpFunToken {
  mint: string
  name: string
  symbol: string
  description: string
  image_uri: string
  metadata_uri: string
  twitter: string | null
  telegram: string | null
  bonding_curve: string
  associated_bonding_curve: string
  creator: string
  created_timestamp: number
  raydium_pool: string | null
  complete: boolean
  virtual_sol_reserves: number
  virtual_token_reserves: number
  total_supply: number
  website: string | null
  show_name: boolean
  king_of_the_hill_timestamp: number | null
  market_cap: number
  reply_count: number
  last_reply: number
  nsfw: boolean
  market_id: string | null
  inverted: boolean | null
  is_currently_live: boolean
  username: string | null
  profile_image: string | null
  usd_market_cap: number
}

// Use our local API route to avoid CORS issues
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || ''

class TokenTrackerAPI {
  private baseURL: string

  constructor(baseURL: string = API_BASE_URL) {
    this.baseURL = baseURL
  }

  /**
   * Fetch all tokens from TokenTracker and enrich with DexScreener and Pump.fun data
   */
  async fetchTokens(): Promise<TokenTrackerToken[]> {
    try {
      const response = await fetch(`/api/tokens`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        // Add cache control to ensure fresh data
        cache: 'no-cache',
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()

      // Ensure we have an array
      if (!Array.isArray(data)) {
        throw new Error('Invalid response format: expected array')
      }

      return data as TokenTrackerToken[]
    } catch (error) {
      console.error('Error fetching tokens from TokenTracker:', error)
      throw error
    }
  }

  /**
   * Fetch price and volume data from DexScreener for a token via our API route
   */
  async fetchDexScreenerData(tokenAddress: string): Promise<DexScreenerPair | null> {
    try {
      const response = await fetch(`/api/dexscreener/${tokenAddress}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        cache: 'no-cache',
      })

      if (!response.ok) {
        return null
      }

      const data: DexScreenerResponse = await response.json()

      // Return the first pair (usually the most liquid one)
      return data.pairs && data.pairs.length > 0 ? data.pairs[0] : null
    } catch (error) {
      console.error(`Error fetching DexScreener data for ${tokenAddress}:`, error)
      return null
    }
  }

  /**
   * Fetch additional data from Pump.fun for a token via our API route
   */
  async fetchPumpFunData(tokenAddress: string): Promise<PumpFunToken | null> {
    try {
      const response = await fetch(`/api/pumpfun/${tokenAddress}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        cache: 'no-cache',
      })

      if (!response.ok) {
        return null
      }

      const data: PumpFunToken = await response.json()
      return data
    } catch (error) {
      console.error(`Error fetching Pump.fun data for ${tokenAddress}:`, error)
      return null
    }
  }

  /**
   * Transform TokenTracker API data to UI format with DexScreener and Pump.fun enrichment
   */
  async transformTokenData(tokens: TokenTrackerToken[]): Promise<TokenTableData[]> {
    const enrichedTokens = await Promise.all(
      tokens.map(async (token) => {
        // Format market cap
        const marketCap = this.formatMarketCap(token.usd_market_cap)

        // Format dates
        const created = this.formatDate(token.created_timestamp)
        const bonded = token.last_trade_timestamp ? this.formatDate(token.last_trade_timestamp) : 'Not bonded'

        // Fetch DexScreener data for price changes and volume
        const dexData = await this.fetchDexScreenerData(token.token_address)

        // Extract price changes and volume from DexScreener data
        let fiveMin = "--"
        let oneHour = "--"
        let sixHour = "--"
        let twentyFourHour = "--"
        let fiveMinVol = "--"
        let oneHourVol = "--"
        let sixHourVol = "--"
        let twentyFourHourVol = "--"
        let sevenDay = "--"
        let chart: "up" | "down" = "up"

        if (dexData) {
          // Format price changes
          if (dexData.priceChange.m5 !== undefined) {
            fiveMin = dexData.priceChange.m5.toFixed(2)
          }
          if (dexData.priceChange.h1 !== undefined) {
            oneHour = dexData.priceChange.h1.toFixed(2)
          }
          if (dexData.priceChange.h6 !== undefined) {
            sixHour = dexData.priceChange.h6.toFixed(2)
          }
          if (dexData.priceChange.h24 !== undefined) {
            twentyFourHour = dexData.priceChange.h24.toFixed(2)
          }

          // Format volume data
          if (dexData.volume.m5 !== undefined) {
            fiveMinVol = this.formatVolume(dexData.volume.m5)
          }
          if (dexData.volume.h1 !== undefined) {
            oneHourVol = this.formatVolume(dexData.volume.h1)
          }
          if (dexData.volume.h6 !== undefined) {
            sixHourVol = this.formatVolume(dexData.volume.h6)
          }
          if (dexData.volume.h24 !== undefined) {
            twentyFourHourVol = this.formatVolume(dexData.volume.h24)
          }

          // Determine chart direction based on 24h price change
          chart = dexData.priceChange.h24 >= 0 ? "up" : "down"
        }

        return {
          id: token.token_address,
          name: token.name,
          symbol: token.symbol,
          marketCap,
          created,
          bonded,
          fiveMin,
          oneHour,
          sixHour,
          twentyFourHour,
          fiveMinVol,
          oneHourVol,
          sixHourVol,
          twentyFourHourVol,
          sevenDay,
          chart,
          tokenAddress: token.token_address,
          imageUrl: token.image_url,
          description: token.description,
          website: token.website || undefined,
          twitter: token.twitter || undefined,
          telegram: token.telegram || undefined,
        }
      })
    )

    return enrichedTokens
  }

  /**
   * Format market cap value
   */
  private formatMarketCap(value: number): string {
    if (value >= 1000000) {
      return `$${(value / 1000000).toFixed(2)}M`
    } else if (value >= 1000) {
      return `$${(value / 1000).toFixed(2)}K`
    } else {
      return `$${value.toFixed(2)}`
    }
  }

  /**
   * Format volume value
   */
  private formatVolume(value: number): string {
    if (value >= 1000000) {
      return `${(value / 1000000).toFixed(1)}M`
    } else if (value >= 1000) {
      return `${(value / 1000).toFixed(1)}K`
    } else {
      return Math.floor(value).toString()
    }
  }

  /**
   * Format timestamp to readable date
   */
  private formatDate(timestamp: number): string {
    const date = new Date(timestamp)
    return date.toLocaleDateString('en-US', {
      month: '2-digit',
      day: '2-digit',
      year: 'numeric'
    })
  }




  /**
   * Get fallback dummy data when API is unavailable
   */
  private getFallbackData(): TokenTableData[] {
    return [
      {
        id: "fallback-1",
        name: "API Unavailable",
        symbol: "N/A",
        marketCap: "$0",
        created: "N/A",
        bonded: "N/A",
        fiveMin: "--",
        oneHour: "--",
        sixHour: "--",
        twentyFourHour: "--",
        fiveMinVol: "--",
        oneHourVol: "--",
        sixHourVol: "--",
        twentyFourHourVol: "--",
        sevenDay: "--",
        chart: "up" as const,
        tokenAddress: "N/A",
        imageUrl: "/placeholder.svg?height=32&width=32",
        description: "TokenTracker API is currently unavailable. Please try again later.",
        website: undefined,
        twitter: undefined,
        telegram: undefined,
      }
    ]
  }

  /**
   * Get tokens with automatic retry on failure and fallback data
   */
  async getTokensWithRetry(maxRetries: number = 3, useFallback: boolean = true): Promise<TokenTableData[]> {
    let lastError: Error | null = null

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const tokens = await this.fetchTokens()
        return await this.transformTokenData(tokens)
      } catch (error) {
        lastError = error as Error
        console.warn(`Attempt ${attempt} failed:`, error)

        if (attempt < maxRetries) {
          // Wait before retrying (exponential backoff)
          await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000))
        }
      }
    }

    if (useFallback) {
      console.warn('All attempts failed, returning fallback data')
      return this.getFallbackData()
    }

    throw lastError || new Error('Failed to fetch tokens after retries')
  }

  /**
   * Check if API is available
   */
  async checkAPIHealth(): Promise<boolean> {
    try {
      const response = await fetch(`/api/tokens`, {
        method: 'HEAD',
        cache: 'no-cache',
      })
      return response.ok
    } catch {
      return false
    }
  }
}

// Export singleton instance
export const tokenTrackerAPI = new TokenTrackerAPI()
export default TokenTrackerAPI

"use client"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { MoreHorizontal } from "lucide-react"
import Link from "next/link"
import { <PERSON>, LineChart, ResponsiveContainer } from "recharts"

import type { TokenTableData } from "@/lib/types"

interface TokenTableProps {
  tokens: TokenTableData[]
}

// Mock data for charts
const upChartData = [
  { value: 10 },
  { value: 15 },
  { value: 13 },
  { value: 17 },
  { value: 20 },
  { value: 25 },
  { value: 23 },
  { value: 30 },
]

const downChartData = [
  { value: 30 },
  { value: 25 },
  { value: 27 },
  { value: 20 },
  { value: 15 },
  { value: 17 },
  { value: 13 },
  { value: 10 },
]

export default function TokenTable({ tokens }: TokenTableProps) {
  return (
    <div className="overflow-x-auto">
      <table className="w-full">
        <thead>
          <tr className="bg-gray-50 text-left">
            <th className="px-4 py-3 text-xs font-medium text-gray-500">Name</th>
            <th className="px-4 py-3 text-xs font-medium text-gray-500">Symbol</th>
            <th className="px-4 py-3 text-xs font-medium text-gray-500">Market Cap</th>
            <th className="px-4 py-3 text-xs font-medium text-gray-500">Created</th>
            <th className="px-4 py-3 text-xs font-medium text-gray-500">Bonded</th>
            <th className="px-4 py-3 text-xs font-medium text-gray-500">5mP</th>
            <th className="px-4 py-3 text-xs font-medium text-gray-500">1hP</th>
            <th className="px-4 py-3 text-xs font-medium text-gray-500">6hP</th>
            <th className="px-4 py-3 text-xs font-medium text-gray-500">24hP</th>
            <th className="px-4 py-3 text-xs font-medium text-gray-500">5mV</th>
            <th className="px-4 py-3 text-xs font-medium text-gray-500">Last 7 days</th>
            <th className="px-4 py-3 text-xs font-medium text-gray-500">Actions</th>
          </tr>
        </thead>
        <tbody>
          {tokens.map((token) => (
            <tr key={token.id} className="border-b border-gray-100 hover:bg-gray-50">
              <td className="px-4 py-3">
                <div className="flex items-center gap-3">
                  <img
                    src={token.imageUrl}
                    alt={token.name}
                    className="w-8 h-8 rounded-full object-cover"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = "/placeholder.svg?height=32&width=32";
                    }}
                  />
                  <Link href={`/token/${token.id}`} className="text-sm font-medium hover:text-indigo-700">
                    {token.name}
                  </Link>
                </div>
              </td>
              <td className="px-4 py-3 text-sm">{token.symbol}</td>
              <td className="px-4 py-3 text-sm text-yellow-500">{token.marketCap}</td>
              <td className="px-4 py-3 text-sm">{token.created}</td>
              <td className="px-4 py-3 text-sm">{token.bonded}</td>
              <td className="px-4 py-3 text-sm">
                <span className={parseFloat(token.fiveMin) >= 0 ? "text-green-500" : "text-red-500"}>
                  {token.fiveMin}%
                </span>
              </td>
              <td className="px-4 py-3 text-sm">
                <span className={parseFloat(token.oneHour) >= 0 ? "text-green-500" : "text-red-500"}>
                  {token.oneHour}%
                </span>
              </td>
              <td className="px-4 py-3 text-sm">
                <span className={parseFloat(token.sixHour) >= 0 ? "text-green-500" : "text-red-500"}>
                  {token.sixHour}%
                </span>
              </td>
              <td className="px-4 py-3 text-sm">
                <span className={parseFloat(token.twentyFourHour) >= 0 ? "text-green-500" : "text-red-500"}>
                  {token.twentyFourHour}%
                </span>
              </td>
              <td className="px-4 py-3 text-sm">
                <span className={parseFloat(token.sevenDay) >= 0 ? "text-green-500" : "text-red-500"}>
                  {token.sevenDay}%
                </span>
              </td>
              <td className="px-4 py-3">
                <div className="h-8 w-20">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={token.chart === "up" ? upChartData : downChartData}>
                      <Line
                        type="monotone"
                        dataKey="value"
                        stroke={token.chart === "up" ? "#10B981" : "#EF4444"}
                        strokeWidth={2}
                        dot={false}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              </td>
              <td className="px-4 py-3">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon" className="h-8 w-8">
                      <MoreHorizontal size={16} />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem>View Details</DropdownMenuItem>
                    <DropdownMenuItem>Add to Watchlist</DropdownMenuItem>
                    <DropdownMenuItem>Set Alert</DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}

// Real-time token scraper that constantly monitors pump.fun and enriches with DexScreener data
import { TokenTrackerToken } from './types'

interface ScrapedToken {
  mint: string
  name: string
  symbol: string
  description: string
  image_uri: string
  website: string | null
  twitter: string | null
  telegram: string | null
  bonding_curve: string
  associated_bonding_curve: string
  creator: string
  created_timestamp: number
  raydium_pool: string | null
  complete: boolean
  virtual_sol_reserves: number
  virtual_token_reserves: number
  total_supply: number
  is_currently_live: boolean
  market_cap: number
  usd_market_cap: number
  last_trade_timestamp?: number
}

interface DexScreenerPair {
  priceUsd: string
  priceChange: {
    m5: number
    h1: number
    h6: number
    h24: number
  }
  volume: {
    m5: number
    h1: number
    h6: number
    h24: number
  }
  liquidity?: {
    usd: number
  }
  marketCap?: number
}

class TokenScraper {
  private trackedTokens = new Map<string, TokenTrackerToken>()
  private isRunning = false
  private scrapeInterval: NodeJS.Timeout | null = null
  private readonly SCRAPE_INTERVAL = 30000 // 30 seconds
  private readonly PUMP_FUN_API = 'https://frontend-api.pump.fun'
  private readonly DEX_SCREENER_API = 'https://api.dexscreener.com/latest/dex'

  constructor() {
    console.log('🚀 Token Scraper initialized')
  }

  /**
   * Start the constant scraping process
   */
  start() {
    if (this.isRunning) {
      console.log('⚠️ Scraper already running')
      return
    }

    console.log('🔄 Starting constant token scraping...')
    this.isRunning = true

    // Run initial scrape
    this.scrapeTokens()

    // Set up interval for constant scraping
    this.scrapeInterval = setInterval(() => {
      this.scrapeTokens()
    }, this.SCRAPE_INTERVAL)

    console.log(`✅ Scraper started - monitoring every ${this.SCRAPE_INTERVAL / 1000} seconds`)
  }

  /**
   * Stop the scraping process
   */
  stop() {
    if (!this.isRunning) {
      return
    }

    console.log('🛑 Stopping token scraper...')
    this.isRunning = false

    if (this.scrapeInterval) {
      clearInterval(this.scrapeInterval)
      this.scrapeInterval = null
    }

    console.log('✅ Token scraper stopped')
  }

  /**
   * Main scraping function - constantly monitors pump.fun
   */
  private async scrapeTokens() {
    try {
      console.log('🔍 Scraping pump.fun for new tokens...')

      // Fetch latest tokens from pump.fun
      const newTokens = await this.fetchPumpFunTokens()
      
      let newTokensFound = 0
      let bondedTokensFound = 0

      for (const token of newTokens) {
        const isNew = await this.processToken(token)
        
        if (isNew.newToken) {
          newTokensFound++
          console.log(`🆕 NEW TOKEN DETECTED: ${token.symbol} (${token.mint})`)
        }
        
        if (isNew.newlyBonded) {
          bondedTokensFound++
          console.log(`🎯 100% BONDED: ${token.symbol} (${token.mint})`)
        }
      }

      console.log(`✅ Scrape complete: ${newTokens.length} tokens checked, ${newTokensFound} new, ${bondedTokensFound} newly bonded`)

    } catch (error) {
      console.error('❌ Error during token scraping:', error)
    }
  }

  /**
   * Fetch latest tokens from pump.fun
   */
  private async fetchPumpFunTokens(): Promise<ScrapedToken[]> {
    try {
      // Get tokens sorted by creation time and recent activity
      const endpoints = [
        `${this.PUMP_FUN_API}/coins?offset=0&limit=50&sort=created_timestamp&order=DESC`,
        `${this.PUMP_FUN_API}/coins?offset=0&limit=30&sort=last_trade_timestamp&order=DESC`
      ]

      const allTokens: ScrapedToken[] = []
      const seenTokens = new Set<string>()

      for (const endpoint of endpoints) {
        try {
          const response = await fetch(endpoint, {
            headers: {
              'User-Agent': 'TokenTracker/1.0',
              'Accept': 'application/json'
            }
          })

          if (response.ok) {
            const tokens = await response.json()
            
            for (const token of tokens) {
              if (!seenTokens.has(token.mint)) {
                seenTokens.add(token.mint)
                allTokens.push(token)
              }
            }
          }
        } catch (error) {
          console.error(`Error fetching from ${endpoint}:`, error)
        }
      }

      return allTokens
    } catch (error) {
      console.error('Error fetching pump.fun tokens:', error)
      return []
    }
  }

  /**
   * Process a token and determine if it should be tracked
   */
  private async processToken(token: ScrapedToken): Promise<{ newToken: boolean; newlyBonded: boolean }> {
    const existingToken = this.trackedTokens.get(token.mint)
    
    let newToken = false
    let newlyBonded = false

    if (!existingToken) {
      // Check if this is a token worth tracking
      const shouldTrack = this.shouldTrackToken(token)
      
      if (shouldTrack) {
        // Add new token
        await this.addToken(token)
        newToken = true
      }
    } else {
      // Update existing token and check for bonding status change
      const wasBonded = existingToken.complete
      const nowBonded = token.complete

      if (!wasBonded && nowBonded) {
        newlyBonded = true
      }

      // Update the token
      await this.updateToken(token)
    }

    return { newToken, newlyBonded }
  }

  /**
   * Determine if a token should be tracked
   */
  private shouldTrackToken(token: ScrapedToken): boolean {
    const now = Date.now()
    const tokenAge = now - token.created_timestamp

    // Track if it's a recent launch (less than 2 hours old)
    const isRecentLaunch = tokenAge < 7200000 // 2 hours

    // Track if it's 100% bonded
    const is100PercentBonded = token.complete

    // Track if it has recent trading activity
    const hasRecentActivity = token.last_trade_timestamp && 
      (now - token.last_trade_timestamp) < 3600000 // 1 hour

    return isRecentLaunch || is100PercentBonded || hasRecentActivity
  }

  /**
   * Add a new token to tracking
   */
  private async addToken(token: ScrapedToken) {
    try {
      // Enrich with DexScreener data
      const dexData = await this.fetchDexScreenerData(token.mint)

      const trackedToken: TokenTrackerToken = {
        token_address: token.mint,
        name: token.name,
        symbol: token.symbol,
        description: token.description,
        image_url: token.image_uri,
        website: token.website,
        twitter: token.twitter,
        telegram: token.telegram,
        created_timestamp: token.created_timestamp,
        last_trade_timestamp: token.last_trade_timestamp,
        usd_market_cap: token.usd_market_cap,
        market_cap: token.market_cap,
        raydium_pool: token.raydium_pool,
        complete: token.complete,
        virtual_sol_reserves: token.virtual_sol_reserves.toString(),
        virtual_token_reserves: token.virtual_token_reserves.toString(),
        total_supply: token.total_supply,
        is_currently_live: token.is_currently_live,
        bonding_curve: token.bonding_curve,
        associated_bonding_curve: token.associated_bonding_curve,
        creator: token.creator,
        metadata_uri: '',
        dex_screener_data: dexData ? {
          price_usd: dexData.priceUsd,
          price_change_5m: dexData.priceChange?.m5,
          price_change_1h: dexData.priceChange?.h1,
          price_change_6h: dexData.priceChange?.h6,
          price_change_24h: dexData.priceChange?.h24,
          volume_5m: dexData.volume?.m5,
          volume_1h: dexData.volume?.h1,
          volume_6h: dexData.volume?.h6,
          volume_24h: dexData.volume?.h24,
          liquidity_usd: dexData.liquidity?.usd,
          market_cap_dex: dexData.marketCap
        } : undefined
      }

      this.trackedTokens.set(token.mint, trackedToken)
      
    } catch (error) {
      console.error(`Error adding token ${token.mint}:`, error)
    }
  }

  /**
   * Update existing token
   */
  private async updateToken(token: ScrapedToken) {
    try {
      const existingToken = this.trackedTokens.get(token.mint)
      if (!existingToken) return

      // Update DexScreener data
      const dexData = await this.fetchDexScreenerData(token.mint)

      const updatedToken: TokenTrackerToken = {
        ...existingToken,
        last_trade_timestamp: token.last_trade_timestamp,
        usd_market_cap: token.usd_market_cap,
        market_cap: token.market_cap,
        raydium_pool: token.raydium_pool,
        complete: token.complete,
        virtual_sol_reserves: token.virtual_sol_reserves.toString(),
        virtual_token_reserves: token.virtual_token_reserves.toString(),
        is_currently_live: token.is_currently_live,
        dex_screener_data: dexData ? {
          price_usd: dexData.priceUsd,
          price_change_5m: dexData.priceChange?.m5,
          price_change_1h: dexData.priceChange?.h1,
          price_change_6h: dexData.priceChange?.h6,
          price_change_24h: dexData.priceChange?.h24,
          volume_5m: dexData.volume?.m5,
          volume_1h: dexData.volume?.h1,
          volume_6h: dexData.volume?.h6,
          volume_24h: dexData.volume?.h24,
          liquidity_usd: dexData.liquidity?.usd,
          market_cap_dex: dexData.marketCap
        } : existingToken.dex_screener_data
      }

      this.trackedTokens.set(token.mint, updatedToken)
      
    } catch (error) {
      console.error(`Error updating token ${token.mint}:`, error)
    }
  }

  /**
   * Fetch DexScreener data for a token
   */
  private async fetchDexScreenerData(tokenAddress: string): Promise<DexScreenerPair | null> {
    try {
      const response = await fetch(`${this.DEX_SCREENER_API}/tokens/${tokenAddress}`, {
        headers: { 'User-Agent': 'TokenTracker/1.0' }
      })

      if (response.ok) {
        const data = await response.json()
        return data.pairs && data.pairs.length > 0 ? data.pairs[0] : null
      }
    } catch (error) {
      // Silently fail for DexScreener - not all tokens will be there
    }
    
    return null
  }

  /**
   * Get all tracked tokens
   */
  getTrackedTokens(): TokenTrackerToken[] {
    return Array.from(this.trackedTokens.values())
      .sort((a, b) => b.created_timestamp - a.created_timestamp)
  }

  /**
   * Get scraper status
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      trackedTokensCount: this.trackedTokens.size,
      scrapeInterval: this.SCRAPE_INTERVAL
    }
  }
}

// Export singleton instance
export const tokenScraper = new TokenScraper()
export default tokenScraper

// Auto-start the scraper when this module is imported (server-side only)
if (typeof window === 'undefined') {
  console.log('🔧 Token scraper module loaded, scheduling auto-start...')

  // Add a small delay to ensure the server is ready
  setTimeout(() => {
    console.log('⏰ Auto-starting token scraper...')
    tokenScraper.start()
  }, 3000) // 3 second delay
}

// Handle graceful shutdown
if (typeof process !== 'undefined') {
  process.on('SIGTERM', () => {
    console.log('Received SIGTERM, stopping token scraper...')
    tokenScraper.stop()
    process.exit(0)
  })

  process.on('SIGINT', () => {
    console.log('Received SIGINT, stopping token scraper...')
    tokenScraper.stop()
    process.exit(0)
  })
}

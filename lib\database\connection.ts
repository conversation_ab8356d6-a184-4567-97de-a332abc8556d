import { MongoClient, Db, Collection } from 'mongodb'
import { 
  TrackedToken, 
  ScrapingLog, 
  MonitoringStatus, 
  PriceHistory, 
  TokenDetectionEvent,
  COLLECTIONS,
  DATABASE_INDEXES
} from './models'

class DatabaseConnection {
  private client: MongoClient | null = null
  private db: Db | null = null
  private connectionString: string

  constructor() {
    this.connectionString = process.env.MONGODB_URI || 'mongodb+srv://wasay:<EMAIL>/Rader'
  }

  /**
   * Connect to MongoDB
   */
  async connect(): Promise<void> {
    try {
      if (this.client && this.db) {
        return // Already connected
      }

      console.log('Connecting to MongoDB...')
      this.client = new MongoClient(this.connectionString)
      await this.client.connect()
      
      // Extract database name from connection string or use default
      const dbName = this.extractDatabaseName() || 'Rader'
      this.db = this.client.db(dbName)
      
      console.log(`Connected to MongoDB database: ${dbName}`)
      
      // Initialize indexes
      await this.initializeIndexes()
      
    } catch (error) {
      console.error('Failed to connect to MongoDB:', error)
      throw error
    }
  }

  /**
   * Disconnect from MongoDB
   */
  async disconnect(): Promise<void> {
    if (this.client) {
      await this.client.close()
      this.client = null
      this.db = null
      console.log('Disconnected from MongoDB')
    }
  }

  /**
   * Get database instance
   */
  getDb(): Db {
    if (!this.db) {
      throw new Error('Database not connected. Call connect() first.')
    }
    return this.db
  }

  /**
   * Get collection with proper typing
   */
  getCollection<T>(collectionName: string): Collection<T> {
    return this.getDb().collection<T>(collectionName)
  }

  /**
   * Get typed collections
   */
  get collections() {
    return {
      trackedTokens: this.getCollection<TrackedToken>(COLLECTIONS.TRACKED_TOKENS),
      scrapingLogs: this.getCollection<ScrapingLog>(COLLECTIONS.SCRAPING_LOGS),
      monitoringStatus: this.getCollection<MonitoringStatus>(COLLECTIONS.MONITORING_STATUS),
      priceHistory: this.getCollection<PriceHistory>(COLLECTIONS.PRICE_HISTORY),
      detectionEvents: this.getCollection<TokenDetectionEvent>(COLLECTIONS.DETECTION_EVENTS)
    }
  }

  /**
   * Initialize database indexes for optimal performance
   */
  private async initializeIndexes(): Promise<void> {
    try {
      console.log('Initializing database indexes...')
      
      // Tracked tokens indexes
      const trackedTokensCollection = this.collections.trackedTokens
      for (const index of DATABASE_INDEXES.TRACKED_TOKENS) {
        await trackedTokensCollection.createIndex(index)
      }
      
      // Unique index for token address
      await trackedTokensCollection.createIndex(
        { tokenAddress: 1 }, 
        { unique: true }
      )
      
      // Scraping logs indexes
      const scrapingLogsCollection = this.collections.scrapingLogs
      for (const index of DATABASE_INDEXES.SCRAPING_LOGS) {
        await scrapingLogsCollection.createIndex(index)
      }
      
      // Monitoring status indexes
      const monitoringStatusCollection = this.collections.monitoringStatus
      for (const index of DATABASE_INDEXES.MONITORING_STATUS) {
        await monitoringStatusCollection.createIndex(index)
      }
      
      // Unique index for service
      await monitoringStatusCollection.createIndex(
        { service: 1 }, 
        { unique: true }
      )
      
      // Price history indexes
      const priceHistoryCollection = this.collections.priceHistory
      for (const index of DATABASE_INDEXES.PRICE_HISTORY) {
        await priceHistoryCollection.createIndex(index)
      }
      
      // Detection events indexes
      const detectionEventsCollection = this.collections.detectionEvents
      for (const index of DATABASE_INDEXES.DETECTION_EVENTS) {
        await detectionEventsCollection.createIndex(index)
      }
      
      console.log('Database indexes initialized successfully')
      
    } catch (error) {
      console.error('Failed to initialize indexes:', error)
      throw error
    }
  }

  /**
   * Extract database name from connection string
   */
  private extractDatabaseName(): string | null {
    try {
      const url = new URL(this.connectionString.replace('mongodb+srv://', 'https://'))
      return url.pathname.substring(1) // Remove leading slash
    } catch {
      return null
    }
  }

  /**
   * Health check
   */
  async healthCheck(): Promise<boolean> {
    try {
      if (!this.db) return false
      await this.db.admin().ping()
      return true
    } catch {
      return false
    }
  }

  /**
   * Get database statistics
   */
  async getStats() {
    const db = this.getDb()
    const stats = await db.stats()
    
    const collectionStats = await Promise.all([
      this.collections.trackedTokens.countDocuments(),
      this.collections.scrapingLogs.countDocuments(),
      this.collections.monitoringStatus.countDocuments(),
      this.collections.priceHistory.countDocuments(),
      this.collections.detectionEvents.countDocuments()
    ])
    
    return {
      database: stats,
      collections: {
        trackedTokens: collectionStats[0],
        scrapingLogs: collectionStats[1],
        monitoringStatus: collectionStats[2],
        priceHistory: collectionStats[3],
        detectionEvents: collectionStats[4]
      }
    }
  }
}

// Singleton instance
export const database = new DatabaseConnection()
export default database

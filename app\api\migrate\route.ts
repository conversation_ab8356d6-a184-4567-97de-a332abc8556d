import { NextRequest, NextResponse } from 'next/server'
import database from '../../../lib/database/connection'
import { TrackedToken } from '../../../lib/database/models'

export async function POST(request: NextRequest) {
  try {
    console.log('Starting migration from TokenTracker API to database...')
    
    await database.connect()

    // Fetch data from the original TokenTracker API
    const response = await fetch('https://tokentracker-fc80b9e9df85.herokuapp.com/tokens', {
      headers: {
        'User-Agent': 'TokenTracker/1.0',
      }
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const tokens = await response.json()
    
    if (!Array.isArray(tokens)) {
      throw new Error('Invalid response format: expected array')
    }

    console.log(`Found ${tokens.length} tokens to migrate`)

    let migratedCount = 0
    let skippedCount = 0

    for (const token of tokens) {
      try {
        // Check if token already exists
        const existingToken = await database.collections.trackedTokens.findOne({
          tokenAddress: token.token_address
        })

        if (existingToken) {
          skippedCount++
          continue
        }

        // Create tracked token from original API data
        const now = new Date()
        
        const trackedToken: TrackedToken = {
          tokenAddress: token.token_address,
          name: token.name,
          symbol: token.symbol,
          description: token.description,
          imageUrl: token.image_url,
          website: token.website || undefined,
          twitter: token.twitter || undefined,
          telegram: token.telegram || undefined,
          
          pumpFunData: {
            mint: token.token_address,
            bondingCurve: token.bonding_curve,
            associatedBondingCurve: token.associated_bonding_curve,
            creator: token.creator,
            createdTimestamp: token.created_timestamp,
            raydiumPool: token.raydium_pool,
            complete: token.complete || false,
            virtualSolReserves: parseFloat(token.virtual_sol_reserves) || 0,
            virtualTokenReserves: parseFloat(token.virtual_token_reserves) || 0,
            totalSupply: token.total_supply,
            marketCap: token.usd_market_cap || 0,
            usdMarketCap: token.usd_market_cap,
            isCurrentlyLive: true,
            lastTradeTimestamp: token.last_trade_timestamp
          },
          
          trackingInfo: {
            firstDetected: now,
            detectionReason: 'manual_add',
            lastUpdated: now,
            isActive: true,
            updateCount: 1
          },
          
          createdAt: now,
          updatedAt: now
        }

        // Insert token
        await database.collections.trackedTokens.insertOne(trackedToken)
        migratedCount++

        console.log(`Migrated: ${token.symbol} (${token.token_address})`)

      } catch (error) {
        console.error(`Error migrating token ${token.token_address}:`, error)
      }
    }

    console.log(`Migration completed: ${migratedCount} migrated, ${skippedCount} skipped`)

    return NextResponse.json({
      success: true,
      message: 'Migration completed successfully',
      stats: {
        totalTokens: tokens.length,
        migrated: migratedCount,
        skipped: skippedCount
      }
    })

  } catch (error) {
    console.error('Migration error:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Migration failed',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function GET() {
  try {
    await database.connect()
    
    const tokenCount = await database.collections.trackedTokens.countDocuments()
    
    return NextResponse.json({
      message: 'Migration endpoint ready',
      currentTokenCount: tokenCount,
      instructions: 'Send POST request to migrate tokens from original API'
    })
  } catch (error) {
    return NextResponse.json(
      { error: 'Database connection failed' },
      { status: 500 }
    )
  }
}

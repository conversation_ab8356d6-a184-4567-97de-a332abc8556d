import database from '../database/connection'
import { Tracked<PERSON>oken, PriceHistory, ScrapingLog, MonitoringStatus } from '../database/models'

// DexScreener API response types
interface DexScreenerPair {
  chainId: string
  dexId: string
  url: string
  pairAddress: string
  baseToken: {
    address: string
    name: string
    symbol: string
  }
  quoteToken: {
    address: string
    name: string
    symbol: string
  }
  priceNative: string
  priceUsd: string
  txns: {
    m5: { buys: number; sells: number }
    h1: { buys: number; sells: number }
    h6: { buys: number; sells: number }
    h24: { buys: number; sells: number }
  }
  volume: {
    m5: number
    h1: number
    h6: number
    h24: number
  }
  priceChange: {
    m5: number
    h1: number
    h6: number
    h24: number
  }
  liquidity?: {
    usd: number
    base: number
    quote: number
  }
  fdv?: number
  marketCap?: number
}

interface DexScreenerResponse {
  schemaVersion: string
  pairs: DexScreenerPair[]
}

export class DexScreenerUpdater {
  private isRunning = false
  private intervalId: NodeJS.Timeout | null = null
  private readonly DEX_SCREENER_API = 'https://api.dexscreener.com/latest/dex'
  private readonly UPDATE_INTERVAL = 60000 // 1 minute
  private readonly BATCH_SIZE = 50
  private readonly RATE_LIMIT_DELAY = 1000 // 1 second between requests

  constructor() {
    this.initializeMonitoringStatus()
  }

  /**
   * Start the DexScreener data updater
   */
  async start(): Promise<void> {
    if (this.isRunning) {
      console.log('DexScreener updater is already running')
      return
    }

    console.log('Starting DexScreener updater...')
    this.isRunning = true

    await this.updateMonitoringStatus('running')

    // Start the update loop
    this.intervalId = setInterval(async () => {
      await this.updateAllTokenData()
    }, this.UPDATE_INTERVAL)

    // Run initial update
    await this.updateAllTokenData()

    console.log(`DexScreener updater started with ${this.UPDATE_INTERVAL}ms interval`)
  }

  /**
   * Stop the DexScreener data updater
   */
  async stop(): Promise<void> {
    if (!this.isRunning) {
      console.log('DexScreener updater is not running')
      return
    }

    console.log('Stopping DexScreener updater...')
    this.isRunning = false

    if (this.intervalId) {
      clearInterval(this.intervalId)
      this.intervalId = null
    }

    await this.updateMonitoringStatus('stopped')
    console.log('DexScreener updater stopped')
  }

  /**
   * Update price and volume data for all tracked tokens
   */
  private async updateAllTokenData(): Promise<void> {
    const startTime = Date.now()
    let tokensProcessed = 0
    let successfulUpdates = 0
    const errors: string[] = []

    try {
      console.log('Starting DexScreener data update cycle...')

      await database.connect()
      
      // Get all active tracked tokens
      const trackedTokens = await database.collections.trackedTokens
        .find({ 'trackingInfo.isActive': true })
        .toArray()

      console.log(`Found ${trackedTokens.length} tokens to update`)

      // Process tokens in batches to respect rate limits
      for (let i = 0; i < trackedTokens.length; i += this.BATCH_SIZE) {
        const batch = trackedTokens.slice(i, i + this.BATCH_SIZE)
        
        for (const token of batch) {
          try {
            tokensProcessed++
            const updated = await this.updateTokenData(token)
            if (updated) {
              successfulUpdates++
            }
            
            // Rate limiting delay
            await this.delay(this.RATE_LIMIT_DELAY)
            
          } catch (error) {
            const errorMsg = `Error updating token ${token.tokenAddress}: ${error}`
            errors.push(errorMsg)
            console.error(errorMsg)
          }
        }
        
        // Longer delay between batches
        if (i + this.BATCH_SIZE < trackedTokens.length) {
          await this.delay(this.RATE_LIMIT_DELAY * 2)
        }
      }

      // Log successful update cycle
      await this.logScrapingResult('success', tokensProcessed, successfulUpdates, errors, Date.now() - startTime)

      console.log(`Update cycle completed: ${tokensProcessed} tokens processed, ${successfulUpdates} successful updates`)

    } catch (error) {
      const errorMsg = `DexScreener update error: ${error}`
      errors.push(errorMsg)
      console.error(errorMsg)

      await this.logScrapingResult('error', tokensProcessed, successfulUpdates, errors, Date.now() - startTime)
      await this.updateMonitoringStatus('error', errorMsg)
    }
  }

  /**
   * Update data for a single token
   */
  private async updateTokenData(token: TrackedToken): Promise<boolean> {
    try {
      // Fetch data from DexScreener
      const dexData = await this.fetchDexScreenerData(token.tokenAddress)
      
      if (!dexData) {
        return false
      }

      const now = new Date()

      // Update token with DexScreener data
      await database.collections.trackedTokens.updateOne(
        { tokenAddress: token.tokenAddress },
        {
          $set: {
            dexScreenerData: {
              pairAddress: dexData.pairAddress,
              priceUsd: dexData.priceUsd,
              priceNative: dexData.priceNative,
              priceChange: {
                m5: dexData.priceChange.m5,
                h1: dexData.priceChange.h1,
                h6: dexData.priceChange.h6,
                h24: dexData.priceChange.h24
              },
              volume: {
                m5: dexData.volume.m5,
                h1: dexData.volume.h1,
                h6: dexData.volume.h6,
                h24: dexData.volume.h24
              },
              liquidity: dexData.liquidity,
              fdv: dexData.fdv,
              marketCap: dexData.marketCap,
              lastUpdated: now
            },
            'trackingInfo.lastUpdated': now,
            updatedAt: now
          },
          $inc: {
            'trackingInfo.updateCount': 1
          }
        }
      )

      // Store price history
      await this.storePriceHistory(token.tokenAddress, dexData)

      return true

    } catch (error) {
      console.error(`Error updating token data for ${token.tokenAddress}:`, error)
      throw error
    }
  }

  /**
   * Fetch data from DexScreener API
   */
  private async fetchDexScreenerData(tokenAddress: string): Promise<DexScreenerPair | null> {
    try {
      const response = await fetch(`${this.DEX_SCREENER_API}/tokens/${tokenAddress}`, {
        headers: {
          'User-Agent': 'TokenTracker/1.0',
          'Accept': 'application/json'
        }
      })

      if (!response.ok) {
        if (response.status === 404) {
          // Token not found on DexScreener (normal for new tokens)
          return null
        }
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data: DexScreenerResponse = await response.json()
      
      // Return the first pair (usually the most liquid one)
      return data.pairs && data.pairs.length > 0 ? data.pairs[0] : null

    } catch (error) {
      console.error(`Error fetching DexScreener data for ${tokenAddress}:`, error)
      return null
    }
  }

  /**
   * Store price history for analytics
   */
  private async storePriceHistory(tokenAddress: string, dexData: DexScreenerPair): Promise<void> {
    try {
      const priceHistory: PriceHistory = {
        tokenAddress,
        timestamp: new Date(),
        priceUsd: parseFloat(dexData.priceUsd),
        volume24h: dexData.volume.h24,
        marketCap: dexData.marketCap || 0,
        source: 'dex_screener'
      }

      await database.collections.priceHistory.insertOne(priceHistory)

      // Clean up old price history (keep last 30 days)
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
      await database.collections.priceHistory.deleteMany({
        tokenAddress,
        timestamp: { $lt: thirtyDaysAgo }
      })

    } catch (error) {
      console.error(`Error storing price history for ${tokenAddress}:`, error)
    }
  }

  /**
   * Get price history for a token
   */
  async getPriceHistory(tokenAddress: string, hours: number = 24): Promise<PriceHistory[]> {
    try {
      await database.connect()
      
      const startTime = new Date(Date.now() - hours * 60 * 60 * 1000)
      
      return await database.collections.priceHistory
        .find({
          tokenAddress,
          timestamp: { $gte: startTime }
        })
        .sort({ timestamp: 1 })
        .toArray()

    } catch (error) {
      console.error(`Error getting price history for ${tokenAddress}:`, error)
      return []
    }
  }

  /**
   * Update data for specific tokens (on-demand)
   */
  async updateSpecificTokens(tokenAddresses: string[]): Promise<void> {
    const startTime = Date.now()
    let tokensProcessed = 0
    let successfulUpdates = 0
    const errors: string[] = []

    try {
      console.log(`Updating specific tokens: ${tokenAddresses.join(', ')}`)

      await database.connect()

      for (const tokenAddress of tokenAddresses) {
        try {
          const token = await database.collections.trackedTokens.findOne({ tokenAddress })
          
          if (token) {
            tokensProcessed++
            const updated = await this.updateTokenData(token)
            if (updated) {
              successfulUpdates++
            }
          }
          
          // Rate limiting delay
          await this.delay(this.RATE_LIMIT_DELAY)
          
        } catch (error) {
          const errorMsg = `Error updating token ${tokenAddress}: ${error}`
          errors.push(errorMsg)
          console.error(errorMsg)
        }
      }

      await this.logScrapingResult('success', tokensProcessed, successfulUpdates, errors, Date.now() - startTime)

    } catch (error) {
      const errorMsg = `Error updating specific tokens: ${error}`
      errors.push(errorMsg)
      console.error(errorMsg)

      await this.logScrapingResult('error', tokensProcessed, successfulUpdates, errors, Date.now() - startTime)
    }
  }

  /**
   * Initialize monitoring status in database
   */
  private async initializeMonitoringStatus(): Promise<void> {
    try {
      await database.connect()
      
      const status: MonitoringStatus = {
        service: 'dex_screener_updater',
        status: 'stopped',
        lastRun: new Date(),
        runCount: 0,
        errorCount: 0,
        configuration: {
          intervalMs: this.UPDATE_INTERVAL,
          batchSize: this.BATCH_SIZE,
          retryAttempts: 3
        }
      }

      await database.collections.monitoringStatus.replaceOne(
        { service: 'dex_screener_updater' },
        status,
        { upsert: true }
      )
    } catch (error) {
      console.error('Error initializing monitoring status:', error)
    }
  }

  /**
   * Update monitoring status
   */
  private async updateMonitoringStatus(status: 'running' | 'stopped' | 'error', error?: string): Promise<void> {
    try {
      const update: any = {
        status,
        lastRun: new Date(),
        $inc: { runCount: 1 }
      }

      if (error) {
        update.lastError = error
        update.$inc.errorCount = 1
      }

      await database.collections.monitoringStatus.updateOne(
        { service: 'dex_screener_updater' },
        update
      )
    } catch (err) {
      console.error('Error updating monitoring status:', err)
    }
  }

  /**
   * Log scraping results
   */
  private async logScrapingResult(
    status: 'success' | 'error' | 'partial',
    tokensProcessed: number,
    successfulUpdates: number,
    errors: string[],
    executionTime: number
  ): Promise<void> {
    try {
      const log: ScrapingLog = {
        service: 'dex_screener',
        action: 'update_token_data',
        status,
        tokensProcessed,
        newTokensFound: successfulUpdates,
        errors: errors.length > 0 ? errors : undefined,
        executionTime,
        timestamp: new Date()
      }

      await database.collections.scrapingLogs.insertOne(log)
    } catch (error) {
      console.error('Error logging scraping result:', error)
    }
  }

  /**
   * Utility function for delays
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * Get monitoring status
   */
  async getStatus(): Promise<MonitoringStatus | null> {
    try {
      await database.connect()
      return await database.collections.monitoringStatus.findOne({ service: 'dex_screener_updater' })
    } catch (error) {
      console.error('Error getting monitoring status:', error)
      return null
    }
  }
}

// Export singleton instance
export const dexScreenerUpdater = new DexScreenerUpdater()
export default dexScreenerUpdater

import { NextRequest } from 'next/server'
import { tokenDetector } from '../../../../lib/services/token-detector'
import { TokenDetectionResult } from '../../../../lib/services/token-detector'

// Store active connections
const connections = new Set<ReadableStreamDefaultController>()

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const events = searchParams.get('events')?.split(',') || ['tokenDetected', 'priceUpdate']

  // Create a readable stream for Server-Sent Events
  const stream = new ReadableStream({
    start(controller) {
      // Add this connection to our set
      connections.add(controller)

      // Send initial connection message
      const data = JSON.stringify({
        type: 'connected',
        timestamp: new Date().toISOString(),
        message: 'Real-time connection established'
      })
      
      controller.enqueue(`data: ${data}\n\n`)

      // Set up event listeners based on requested events
      if (events.includes('tokenDetected')) {
        const tokenDetectedHandler = (result: TokenDetectionResult) => {
          try {
            const data = JSON.stringify({
              type: 'tokenDetected',
              timestamp: new Date().toISOString(),
              data: {
                token: {
                  tokenAddress: result.token.tokenAddress,
                  name: result.token.name,
                  symbol: result.token.symbol,
                  imageUrl: result.token.imageUrl,
                  marketCap: result.token.pumpFunData.usdMarketCap,
                  detectionReason: result.token.trackingInfo.detectionReason,
                  isNewToken: result.isNewToken
                },
                event: {
                  eventType: result.event.eventType,
                  detectedAt: result.event.detectedAt
                }
              }
            })
            
            controller.enqueue(`data: ${data}\n\n`)
          } catch (error) {
            console.error('Error sending token detection event:', error)
          }
        }

        tokenDetector.on('tokenDetected', tokenDetectedHandler)

        // Clean up listener when connection closes
        const cleanup = () => {
          tokenDetector.off('tokenDetected', tokenDetectedHandler)
          connections.delete(controller)
        }

        // Store cleanup function for later use
        ;(controller as any).cleanup = cleanup
      }

      // Send periodic heartbeat
      const heartbeatInterval = setInterval(() => {
        try {
          const heartbeat = JSON.stringify({
            type: 'heartbeat',
            timestamp: new Date().toISOString()
          })
          controller.enqueue(`data: ${heartbeat}\n\n`)
        } catch (error) {
          // Connection closed
          clearInterval(heartbeatInterval)
          if ((controller as any).cleanup) {
            ;(controller as any).cleanup()
          }
        }
      }, 30000) // 30 seconds

      // Store interval for cleanup
      ;(controller as any).heartbeatInterval = heartbeatInterval
    },

    cancel() {
      // Clean up when connection is closed
      connections.delete(controller)
      if ((controller as any).cleanup) {
        ;(controller as any).cleanup()
      }
      if ((controller as any).heartbeatInterval) {
        clearInterval((controller as any).heartbeatInterval)
      }
    }
  })

  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control'
    }
  })
}

// Utility function to broadcast messages to all connected clients
export function broadcastToClients(message: any) {
  const data = JSON.stringify(message)
  
  connections.forEach(controller => {
    try {
      controller.enqueue(`data: ${data}\n\n`)
    } catch (error) {
      // Remove dead connections
      connections.delete(controller)
    }
  })
}

// Export for use in other parts of the application
export { connections }

import { pumpFunMonitor } from './pump-fun-monitor'
import { dexScreenerUpdater } from './dexscreener-updater'
import database from '../database/connection'
import { MonitoringStatus } from '../database/models'

export class BackgroundJobManager {
  private isInitialized = false
  private healthCheckInterval: NodeJS.Timeout | null = null
  private readonly HEALTH_CHECK_INTERVAL = 300000 // 5 minutes

  /**
   * Initialize and start all background services
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      console.log('Background job manager already initialized')
      return
    }

    try {
      console.log('Initializing background job manager...')

      // Connect to database
      await database.connect()

      // Start monitoring services
      await this.startAllServices()

      // Start health monitoring
      this.startHealthMonitoring()

      this.isInitialized = true
      console.log('Background job manager initialized successfully')

    } catch (error) {
      console.error('Failed to initialize background job manager:', error)
      throw error
    }
  }

  /**
   * Start all monitoring services
   */
  private async startAllServices(): Promise<void> {
    try {
      console.log('Starting all monitoring services...')

      // Start Pump.fun monitor
      await pumpFunMonitor.start()

      // Wait a bit before starting DexScreener updater to stagger the load
      await this.delay(10000) // 10 seconds

      // Start DexScreener updater
      await dexScreenerUpdater.start()

      console.log('All monitoring services started')

    } catch (error) {
      console.error('Error starting monitoring services:', error)
      throw error
    }
  }

  /**
   * Stop all monitoring services
   */
  async stopAllServices(): Promise<void> {
    try {
      console.log('Stopping all monitoring services...')

      // Stop services
      await pumpFunMonitor.stop()
      await dexScreenerUpdater.stop()

      // Stop health monitoring
      if (this.healthCheckInterval) {
        clearInterval(this.healthCheckInterval)
        this.healthCheckInterval = null
      }

      this.isInitialized = false
      console.log('All monitoring services stopped')

    } catch (error) {
      console.error('Error stopping monitoring services:', error)
      throw error
    }
  }

  /**
   * Restart all services
   */
  async restartAllServices(): Promise<void> {
    console.log('Restarting all monitoring services...')
    await this.stopAllServices()
    await this.delay(5000) // Wait 5 seconds
    await this.startAllServices()
  }

  /**
   * Start health monitoring for all services
   */
  private startHealthMonitoring(): void {
    console.log('Starting health monitoring...')

    this.healthCheckInterval = setInterval(async () => {
      await this.performHealthCheck()
    }, this.HEALTH_CHECK_INTERVAL)

    // Perform initial health check
    setTimeout(() => this.performHealthCheck(), 30000) // After 30 seconds
  }

  /**
   * Perform health check on all services
   */
  private async performHealthCheck(): Promise<void> {
    try {
      console.log('Performing health check...')

      const services = [
        { name: 'pump_fun_monitor', service: pumpFunMonitor },
        { name: 'dex_screener_updater', service: dexScreenerUpdater }
      ]

      for (const { name, service } of services) {
        try {
          const status = await service.getStatus()
          
          if (!status) {
            console.warn(`No status found for ${name}`)
            continue
          }

          // Check if service is running but hasn't updated recently
          const timeSinceLastRun = Date.now() - status.lastRun.getTime()
          const expectedInterval = status.configuration.intervalMs * 2 // Allow 2x the interval

          if (status.status === 'running' && timeSinceLastRun > expectedInterval) {
            console.warn(`Service ${name} appears to be stuck. Last run: ${status.lastRun}`)
            // Could implement auto-restart logic here
          }

          // Check error rate
          const errorRate = status.errorCount / Math.max(status.runCount, 1)
          if (errorRate > 0.5) {
            console.warn(`Service ${name} has high error rate: ${(errorRate * 100).toFixed(1)}%`)
          }

        } catch (error) {
          console.error(`Health check failed for ${name}:`, error)
        }
      }

      // Check database health
      const dbHealthy = await database.healthCheck()
      if (!dbHealthy) {
        console.error('Database health check failed')
      }

    } catch (error) {
      console.error('Health check error:', error)
    }
  }

  /**
   * Get status of all services
   */
  async getAllServiceStatus(): Promise<{
    pumpFunMonitor: MonitoringStatus | null
    dexScreenerUpdater: MonitoringStatus | null
    database: boolean
  }> {
    try {
      const [pumpFunStatus, dexScreenerStatus, dbHealth] = await Promise.all([
        pumpFunMonitor.getStatus(),
        dexScreenerUpdater.getStatus(),
        database.healthCheck()
      ])

      return {
        pumpFunMonitor: pumpFunStatus,
        dexScreenerUpdater: dexScreenerStatus,
        database: dbHealth
      }
    } catch (error) {
      console.error('Error getting service status:', error)
      return {
        pumpFunMonitor: null,
        dexScreenerUpdater: null,
        database: false
      }
    }
  }

  /**
   * Get system statistics
   */
  async getSystemStats() {
    try {
      await database.connect()
      
      const [dbStats, recentLogs] = await Promise.all([
        database.getStats(),
        this.getRecentLogs()
      ])

      return {
        database: dbStats,
        recentActivity: recentLogs
      }
    } catch (error) {
      console.error('Error getting system stats:', error)
      return null
    }
  }

  /**
   * Get recent activity logs
   */
  private async getRecentLogs() {
    try {
      const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000)
      
      const [scrapingLogs, detectionEvents] = await Promise.all([
        database.collections.scrapingLogs
          .find({ timestamp: { $gte: oneDayAgo } })
          .sort({ timestamp: -1 })
          .limit(50)
          .toArray(),
        
        database.collections.detectionEvents
          .find({ detectedAt: { $gte: oneDayAgo } })
          .sort({ detectedAt: -1 })
          .limit(20)
          .toArray()
      ])

      return {
        scrapingLogs,
        detectionEvents
      }
    } catch (error) {
      console.error('Error getting recent logs:', error)
      return {
        scrapingLogs: [],
        detectionEvents: []
      }
    }
  }

  /**
   * Manual trigger for token detection
   */
  async triggerTokenDetection(): Promise<void> {
    try {
      console.log('Manually triggering token detection...')
      
      // This would trigger the pump.fun monitor to run immediately
      // For now, we'll just log it since the monitor runs on its own schedule
      console.log('Token detection trigger logged')
      
    } catch (error) {
      console.error('Error triggering token detection:', error)
      throw error
    }
  }

  /**
   * Manual trigger for price data update
   */
  async triggerPriceUpdate(tokenAddresses?: string[]): Promise<void> {
    try {
      console.log('Manually triggering price data update...')
      
      if (tokenAddresses && tokenAddresses.length > 0) {
        await dexScreenerUpdater.updateSpecificTokens(tokenAddresses)
      } else {
        console.log('Full price update will occur on next scheduled cycle')
      }
      
    } catch (error) {
      console.error('Error triggering price update:', error)
      throw error
    }
  }

  /**
   * Cleanup old data
   */
  async cleanupOldData(): Promise<void> {
    try {
      console.log('Cleaning up old data...')
      
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
      const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)

      // Clean up old scraping logs (keep 7 days)
      const scrapingLogsDeleted = await database.collections.scrapingLogs.deleteMany({
        timestamp: { $lt: sevenDaysAgo }
      })

      // Clean up old price history (keep 30 days)
      const priceHistoryDeleted = await database.collections.priceHistory.deleteMany({
        timestamp: { $lt: thirtyDaysAgo }
      })

      // Clean up processed detection events (keep 30 days)
      const detectionEventsDeleted = await database.collections.detectionEvents.deleteMany({
        detectedAt: { $lt: thirtyDaysAgo },
        processed: true
      })

      console.log(`Cleanup completed:`)
      console.log(`- Scraping logs deleted: ${scrapingLogsDeleted.deletedCount}`)
      console.log(`- Price history deleted: ${priceHistoryDeleted.deletedCount}`)
      console.log(`- Detection events deleted: ${detectionEventsDeleted.deletedCount}`)

    } catch (error) {
      console.error('Error during cleanup:', error)
      throw error
    }
  }

  /**
   * Utility function for delays
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * Check if manager is initialized
   */
  get initialized(): boolean {
    return this.isInitialized
  }
}

// Export singleton instance
export const backgroundJobManager = new BackgroundJobManager()
export default backgroundJobManager

// Auto-initialize when the module is imported in a server environment
if (typeof window === 'undefined' && process.env.NODE_ENV !== 'test') {
  // Only auto-start in production or when explicitly enabled
  if (process.env.NODE_ENV === 'production' || process.env.AUTO_START_BACKGROUND_JOBS === 'true') {
    backgroundJobManager.initialize().catch(error => {
      console.error('Failed to auto-initialize background job manager:', error)
    })
  }
}

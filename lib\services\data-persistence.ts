import database from '../database/connection'
import { TrackedToken, PriceHistory, ScrapingLog, TokenDetectionEvent, MonitoringStatus } from '../database/models'
import { ObjectId } from 'mongodb'

export class DataPersistenceService {
  
  /**
   * Token Management
   */
  async saveToken(token: TrackedToken): Promise<ObjectId> {
    try {
      await database.connect()
      const result = await database.collections.trackedTokens.insertOne(token)
      return result.insertedId
    } catch (error) {
      console.error('Error saving token:', error)
      throw error
    }
  }

  async updateToken(tokenAddress: string, updates: Partial<TrackedToken>): Promise<boolean> {
    try {
      await database.connect()
      const result = await database.collections.trackedTokens.updateOne(
        { tokenAddress },
        { 
          $set: { 
            ...updates, 
            updatedAt: new Date() 
          } 
        }
      )
      return result.modifiedCount > 0
    } catch (error) {
      console.error('Error updating token:', error)
      throw error
    }
  }

  async getToken(tokenAddress: string): Promise<TrackedToken | null> {
    try {
      await database.connect()
      return await database.collections.trackedTokens.findOne({ tokenAddress })
    } catch (error) {
      console.error('Error getting token:', error)
      throw error
    }
  }

  async getTokens(filter: any = {}, options: {
    limit?: number
    skip?: number
    sort?: any
  } = {}): Promise<TrackedToken[]> {
    try {
      await database.connect()
      
      let query = database.collections.trackedTokens.find(filter)
      
      if (options.sort) {
        query = query.sort(options.sort)
      }
      
      if (options.skip) {
        query = query.skip(options.skip)
      }
      
      if (options.limit) {
        query = query.limit(options.limit)
      }
      
      return await query.toArray()
    } catch (error) {
      console.error('Error getting tokens:', error)
      throw error
    }
  }

  async deleteToken(tokenAddress: string): Promise<boolean> {
    try {
      await database.connect()
      const result = await database.collections.trackedTokens.deleteOne({ tokenAddress })
      return result.deletedCount > 0
    } catch (error) {
      console.error('Error deleting token:', error)
      throw error
    }
  }

  async getActiveTokens(): Promise<TrackedToken[]> {
    return this.getTokens(
      { 'trackingInfo.isActive': true },
      { sort: { 'trackingInfo.firstDetected': -1 } }
    )
  }

  async getTokensByDetectionReason(reason: 'new_launch' | '100_percent_bonded' | 'manual_add'): Promise<TrackedToken[]> {
    return this.getTokens(
      { 'trackingInfo.detectionReason': reason },
      { sort: { 'trackingInfo.firstDetected': -1 } }
    )
  }

  /**
   * Price History Management
   */
  async savePriceHistory(priceHistory: PriceHistory): Promise<ObjectId> {
    try {
      await database.connect()
      const result = await database.collections.priceHistory.insertOne(priceHistory)
      return result.insertedId
    } catch (error) {
      console.error('Error saving price history:', error)
      throw error
    }
  }

  async getPriceHistory(
    tokenAddress: string, 
    timeframe: { start: Date; end: Date }
  ): Promise<PriceHistory[]> {
    try {
      await database.connect()
      return await database.collections.priceHistory
        .find({
          tokenAddress,
          timestamp: {
            $gte: timeframe.start,
            $lte: timeframe.end
          }
        })
        .sort({ timestamp: 1 })
        .toArray()
    } catch (error) {
      console.error('Error getting price history:', error)
      throw error
    }
  }

  async getLatestPrice(tokenAddress: string): Promise<PriceHistory | null> {
    try {
      await database.connect()
      return await database.collections.priceHistory
        .findOne(
          { tokenAddress },
          { sort: { timestamp: -1 } }
        )
    } catch (error) {
      console.error('Error getting latest price:', error)
      throw error
    }
  }

  async cleanupOldPriceHistory(olderThan: Date): Promise<number> {
    try {
      await database.connect()
      const result = await database.collections.priceHistory.deleteMany({
        timestamp: { $lt: olderThan }
      })
      return result.deletedCount
    } catch (error) {
      console.error('Error cleaning up price history:', error)
      throw error
    }
  }

  /**
   * Detection Events Management
   */
  async saveDetectionEvent(event: TokenDetectionEvent): Promise<ObjectId> {
    try {
      await database.connect()
      const result = await database.collections.detectionEvents.insertOne(event)
      return result.insertedId
    } catch (error) {
      console.error('Error saving detection event:', error)
      throw error
    }
  }

  async getUnprocessedDetectionEvents(): Promise<TokenDetectionEvent[]> {
    try {
      await database.connect()
      return await database.collections.detectionEvents
        .find({ processed: false })
        .sort({ detectedAt: 1 })
        .toArray()
    } catch (error) {
      console.error('Error getting unprocessed detection events:', error)
      throw error
    }
  }

  async markDetectionEventProcessed(
    eventId: ObjectId, 
    error?: string
  ): Promise<boolean> {
    try {
      await database.connect()
      const update: any = {
        processed: true,
        processedAt: new Date()
      }
      
      if (error) {
        update.error = error
      }
      
      const result = await database.collections.detectionEvents.updateOne(
        { _id: eventId },
        { $set: update }
      )
      return result.modifiedCount > 0
    } catch (error) {
      console.error('Error marking detection event as processed:', error)
      throw error
    }
  }

  async getDetectionEvents(
    filter: any = {},
    options: { limit?: number; skip?: number } = {}
  ): Promise<TokenDetectionEvent[]> {
    try {
      await database.connect()
      
      let query = database.collections.detectionEvents
        .find(filter)
        .sort({ detectedAt: -1 })
      
      if (options.skip) {
        query = query.skip(options.skip)
      }
      
      if (options.limit) {
        query = query.limit(options.limit)
      }
      
      return await query.toArray()
    } catch (error) {
      console.error('Error getting detection events:', error)
      throw error
    }
  }

  /**
   * Scraping Logs Management
   */
  async saveScrapingLog(log: ScrapingLog): Promise<ObjectId> {
    try {
      await database.connect()
      const result = await database.collections.scrapingLogs.insertOne(log)
      return result.insertedId
    } catch (error) {
      console.error('Error saving scraping log:', error)
      throw error
    }
  }

  async getScrapingLogs(
    service?: 'pump_fun' | 'dex_screener',
    timeframe?: { start: Date; end: Date },
    options: { limit?: number; skip?: number } = {}
  ): Promise<ScrapingLog[]> {
    try {
      await database.connect()
      
      const filter: any = {}
      
      if (service) {
        filter.service = service
      }
      
      if (timeframe) {
        filter.timestamp = {
          $gte: timeframe.start,
          $lte: timeframe.end
        }
      }
      
      let query = database.collections.scrapingLogs
        .find(filter)
        .sort({ timestamp: -1 })
      
      if (options.skip) {
        query = query.skip(options.skip)
      }
      
      if (options.limit) {
        query = query.limit(options.limit)
      }
      
      return await query.toArray()
    } catch (error) {
      console.error('Error getting scraping logs:', error)
      throw error
    }
  }

  async cleanupOldScrapingLogs(olderThan: Date): Promise<number> {
    try {
      await database.connect()
      const result = await database.collections.scrapingLogs.deleteMany({
        timestamp: { $lt: olderThan }
      })
      return result.deletedCount
    } catch (error) {
      console.error('Error cleaning up scraping logs:', error)
      throw error
    }
  }

  /**
   * Monitoring Status Management
   */
  async saveMonitoringStatus(status: MonitoringStatus): Promise<void> {
    try {
      await database.connect()
      await database.collections.monitoringStatus.replaceOne(
        { service: status.service },
        status,
        { upsert: true }
      )
    } catch (error) {
      console.error('Error saving monitoring status:', error)
      throw error
    }
  }

  async getMonitoringStatus(service?: string): Promise<MonitoringStatus[]> {
    try {
      await database.connect()
      
      const filter = service ? { service } : {}
      
      return await database.collections.monitoringStatus
        .find(filter)
        .toArray()
    } catch (error) {
      console.error('Error getting monitoring status:', error)
      throw error
    }
  }

  async updateMonitoringStatus(
    service: string,
    updates: Partial<MonitoringStatus>
  ): Promise<boolean> {
    try {
      await database.connect()
      const result = await database.collections.monitoringStatus.updateOne(
        { service },
        { $set: updates }
      )
      return result.modifiedCount > 0
    } catch (error) {
      console.error('Error updating monitoring status:', error)
      throw error
    }
  }

  /**
   * Analytics and Statistics
   */
  async getTokenStatistics(): Promise<{
    total: number
    active: number
    newLaunches: number
    bondedTokens: number
    byDetectionReason: Record<string, number>
  }> {
    try {
      await database.connect()
      
      const [total, active, newLaunches, bondedTokens, byReason] = await Promise.all([
        database.collections.trackedTokens.countDocuments(),
        database.collections.trackedTokens.countDocuments({ 'trackingInfo.isActive': true }),
        database.collections.trackedTokens.countDocuments({ 'trackingInfo.detectionReason': 'new_launch' }),
        database.collections.trackedTokens.countDocuments({ 'trackingInfo.detectionReason': '100_percent_bonded' }),
        database.collections.trackedTokens.aggregate([
          {
            $group: {
              _id: '$trackingInfo.detectionReason',
              count: { $sum: 1 }
            }
          }
        ]).toArray()
      ])

      const byDetectionReason: Record<string, number> = {}
      byReason.forEach(item => {
        byDetectionReason[item._id] = item.count
      })

      return {
        total,
        active,
        newLaunches,
        bondedTokens,
        byDetectionReason
      }
    } catch (error) {
      console.error('Error getting token statistics:', error)
      throw error
    }
  }

  async getRecentActivity(hours: number = 24): Promise<{
    newTokens: number
    detectionEvents: number
    scrapingRuns: number
    errors: number
  }> {
    try {
      await database.connect()
      
      const startTime = new Date(Date.now() - hours * 60 * 60 * 1000)
      
      const [newTokens, detectionEvents, scrapingRuns, errors] = await Promise.all([
        database.collections.trackedTokens.countDocuments({
          'trackingInfo.firstDetected': { $gte: startTime }
        }),
        database.collections.detectionEvents.countDocuments({
          detectedAt: { $gte: startTime }
        }),
        database.collections.scrapingLogs.countDocuments({
          timestamp: { $gte: startTime }
        }),
        database.collections.scrapingLogs.countDocuments({
          timestamp: { $gte: startTime },
          status: 'error'
        })
      ])

      return {
        newTokens,
        detectionEvents,
        scrapingRuns,
        errors
      }
    } catch (error) {
      console.error('Error getting recent activity:', error)
      throw error
    }
  }

  /**
   * Database Maintenance
   */
  async performMaintenance(): Promise<{
    priceHistoryDeleted: number
    scrapingLogsDeleted: number
    detectionEventsDeleted: number
  }> {
    try {
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
      const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)

      const [priceHistoryDeleted, scrapingLogsDeleted, detectionEventsDeleted] = await Promise.all([
        this.cleanupOldPriceHistory(thirtyDaysAgo),
        this.cleanupOldScrapingLogs(sevenDaysAgo),
        database.collections.detectionEvents.deleteMany({
          detectedAt: { $lt: thirtyDaysAgo },
          processed: true
        }).then(result => result.deletedCount)
      ])

      return {
        priceHistoryDeleted,
        scrapingLogsDeleted,
        detectionEventsDeleted
      }
    } catch (error) {
      console.error('Error performing maintenance:', error)
      throw error
    }
  }
}

// Export singleton instance
export const dataPersistence = new DataPersistenceService()
export default dataPersistence
